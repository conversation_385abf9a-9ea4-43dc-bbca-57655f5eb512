import { json } from '@sveltejs/kit';
import type { <PERSON><PERSON>H<PERSON><PERSON> } from './$types';
import { verifyJWT, verifyPassword, hashPassword } from '$lib/server/auth.js';
import { getUserById, updateUserPassword } from '$lib/server/db/operations.js';

export const PUT: RequestHandler = async ({ request, cookies }) => {
  try {
    const token = cookies.get('auth-token');
    if (!token) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    const payload = verifyJWT(token);
    if (!payload) {
      return json({ error: 'Invalid token' }, { status: 401 });
    }

    const { currentPassword, newPassword } = await request.json();

    // Validate input
    if (!currentPassword || !newPassword) {
      return json({ error: 'Current password and new password are required' }, { status: 400 });
    }

    if (newPassword.length < 6) {
      return json({ error: 'New password must be at least 6 characters long' }, { status: 400 });
    }

    // Get user
    const user = await getUserById(payload.userId);
    if (!user) {
      return json({ error: 'User not found' }, { status: 404 });
    }

    // Verify current password
    const isValidPassword = await verifyPassword(currentPassword, user.password);
    if (!isValidPassword) {
      return json({ error: 'Current password is incorrect' }, { status: 400 });
    }

    // Hash new password
    const hashedNewPassword = await hashPassword(newPassword);

    // Update password
    await updateUserPassword(payload.userId, hashedNewPassword);

    return json({ message: 'Password updated successfully' });
  } catch (error) {
    console.error('Change password error:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
};
