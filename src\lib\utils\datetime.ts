import { format } from 'date-fns';

export const DEFAULT_TZ = 'Asia/Kuala_Lumpur';

// Timezone offset helpers (simplified approach)
const TIMEZONE_OFFSETS: Record<string, number> = {
  'Asia/Kuala_Lumpur': 8,
  'Asia/Singapore': 8,
  'Asia/Jakarta': 7,
  'Asia/Bangkok': 7,
  'Asia/Manila': 8,
  'Asia/Hong_Kong': 8,
  'Asia/Shanghai': 8,
  'Asia/Tokyo': 9,
  'UTC': 0,
  'America/New_York': -5, // EST (simplified, doesn't handle DST)
  'America/Los_Angeles': -8, // PST (simplified)
  'Europe/London': 0 // GMT (simplified)
};

// Combine local date/time in a specific timezone into a UTC ISO string
export function toUTCISO(dateStr: string | null | undefined, timeStr: string | null | undefined, tz: string = DEFAULT_TZ): string | null {
  if (!dateStr) return null;
  const time = (timeStr && timeStr.trim()) ? timeStr : '08:00'; // 统一默认时间为 08:00
  const localISO = `${dateStr}T${time}:00`;
  const localDate = new Date(localISO);

  // Apply timezone offset to get UTC
  const offsetHours = TIMEZONE_OFFSETS[tz] || 8;
  const utcDate = new Date(localDate.getTime() - (offsetHours * 60 * 60 * 1000));
  return utcDate.toISOString();
}

// Convert a UTC ISO string to local date/time parts in a timezone
export function fromUTCToParts(utcISO: string, tz: string = DEFAULT_TZ): { date: string; time: string } {
  const utcDate = new Date(utcISO);
  const offsetHours = TIMEZONE_OFFSETS[tz] || 8;
  const localDate = new Date(utcDate.getTime() + (offsetHours * 60 * 60 * 1000));

  return {
    date: format(localDate, 'yyyy-MM-dd'),
    time: format(localDate, 'HH:mm')
  };
}

// Human friendly format for display in a timezone
export function formatDisplay(utcISO: string | null | undefined, tz: string = DEFAULT_TZ): string {
  if (!utcISO) return 'No due date';
  const utcDate = new Date(utcISO);
  const offsetHours = TIMEZONE_OFFSETS[tz] || 8;
  const localDate = new Date(utcDate.getTime() + (offsetHours * 60 * 60 * 1000));
  return format(localDate, 'PPpp'); // e.g., Aug 10, 2025 at 9:00 AM
}

// Helpers to compare with today/tomorrow/yesterday in a timezone
export function relativeLabel(utcISO: string | null | undefined, tz: string = DEFAULT_TZ): string {
  if (!utcISO) return 'No due date';
  const utcDate = new Date(utcISO);
  const offsetHours = TIMEZONE_OFFSETS[tz] || 8;
  const localDate = new Date(utcDate.getTime() + (offsetHours * 60 * 60 * 1000));

  const now = new Date(); // 保持本地时间，不进行时区转换

  const startOf = (d: Date) => new Date(d.getFullYear(), d.getMonth(), d.getDate());
  const day = startOf(localDate);
  const today = startOf(now); // 使用本地的今天

  const diffDays = Math.round((day.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
  const timePart = format(localDate, 'p');

  if (diffDays === 0) return `Today at ${timePart}`;
  if (diffDays === 1) return `Tomorrow at ${timePart}`;
  if (diffDays === -1) return `Yesterday at ${timePart}`;
  if (diffDays > 0) return `${format(localDate, 'P')} at ${timePart}`;
  return `${Math.abs(diffDays)} days overdue (${format(localDate, 'P')} at ${timePart})`;
}

// 统一的日期格式化函数 - 用于所有显示场景
export function formatUnifiedDate(utcISO: string | null | undefined, tz: string = DEFAULT_TZ, includeTime: boolean = true): string {
  if (!utcISO) return 'No due date';
  const utcDate = new Date(utcISO);
  const offsetHours = TIMEZONE_OFFSETS[tz] || 8;
  const localDate = new Date(utcDate.getTime() + (offsetHours * 60 * 60 * 1000));

  if (includeTime) {
    return format(localDate, 'MMM d, yyyy \'at\' h:mm a'); // Aug 20, 2025 at 8:00 AM
  } else {
    return format(localDate, 'MMM d, yyyy'); // Aug 20, 2025
  }
}

// 简短的日期格式 - 用于卡片和列表
export function formatShortDate(utcISO: string | null | undefined, tz: string = DEFAULT_TZ): string {
  if (!utcISO) return 'No due date';
  const utcDate = new Date(utcISO);
  const offsetHours = TIMEZONE_OFFSETS[tz] || 8;
  const localDate = new Date(utcDate.getTime() + (offsetHours * 60 * 60 * 1000));

  const now = new Date(); // 保持本地时间，不进行时区转换

  const startOf = (d: Date) => new Date(d.getFullYear(), d.getMonth(), d.getDate());
  const day = startOf(localDate);
  const today = startOf(now); // 使用本地的今天

  const diffDays = Math.round((day.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

  if (diffDays === 0) return 'Today';
  if (diffDays === 1) return 'Tomorrow';
  if (diffDays === -1) return 'Yesterday';
  return format(localDate, 'MMM d'); // Aug 20
}

