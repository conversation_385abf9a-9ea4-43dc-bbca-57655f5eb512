<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import { fade, fly } from 'svelte/transition';
  import { quintOut } from 'svelte/easing';

  export let taskId: string;
  export let taskTitle: string = '';

  const dispatch = createEventDispatcher();
  let showMenu = false;
  let menuButton: HTMLButtonElement;

  function toggleMenu() {
    showMenu = !showMenu;
  }

  function closeMenu() {
    showMenu = false;
  }

  function handleEdit() {
    dispatch('edit', taskId);
    closeMenu();
  }

  function handleDelete() {
    dispatch('delete', taskId);
    closeMenu();
  }

  function handleClickOutside(event: MouseEvent) {
    if (menuButton && !menuButton.contains(event.target as Node)) {
      closeMenu();
    }
  }

  function handleKeydown(event: KeyboardEvent) {
    if (event.key === 'Escape') {
      closeMenu();
    }
  }
</script>

<svelte:window on:click={handleClickOutside} on:keydown={handleKeydown} />

<div class="task-action-menu">
  <button
    bind:this={menuButton}
    class="menu-trigger"
    on:click|stopPropagation={toggleMenu}
    aria-label="Task actions"
    aria-expanded={showMenu}
    aria-haspopup="true"
  >
    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
    </svg>
  </button>

  {#if showMenu}
    <div 
      class="menu-dropdown"
      transition:fly={{ y: -10, duration: 200, easing: quintOut }}
      on:click|stopPropagation
    >
      <button
        class="menu-item edit-item"
        on:click={handleEdit}
      >
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
        </svg>
        <span>Edit Task</span>
      </button>
      
      <div class="menu-divider"></div>
      
      <button
        class="menu-item delete-item"
        on:click={handleDelete}
      >
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
        </svg>
        <span>Delete Task</span>
      </button>
    </div>
  {/if}
</div>

<style>
  .task-action-menu {
    @apply relative;
  }

  .menu-trigger {
    @apply w-12 h-12 rounded-full flex items-center justify-center;
    @apply text-slate-700 hover:text-slate-900 bg-slate-50 hover:bg-slate-100;
    @apply transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-slate-300;
    @apply active:scale-95 border border-slate-200 shadow-sm;

    /* Subtle pulse animation to indicate interactivity */
    animation: gentle-pulse 3s ease-in-out infinite;
  }

  .menu-dropdown {
    @apply absolute right-0 top-12 z-50 min-w-48;
    @apply rounded-xl border border-slate-200/60 shadow-lg;
    @apply py-2;
    
    /* Glassmorphism effect */
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    box-shadow: 
      0 10px 40px rgba(0, 0, 0, 0.1),
      0 4px 16px rgba(0, 0, 0, 0.06),
      inset 0 1px 0 rgba(255, 255, 255, 0.8);
  }

  .menu-item {
    @apply w-full px-4 py-3 flex items-center gap-3 text-left;
    @apply text-sm font-medium transition-all duration-200;
    @apply hover:bg-slate-50 focus:outline-none focus:bg-slate-50;
  }

  .edit-item {
    @apply text-slate-700 hover:text-slate-900;
  }

  .delete-item {
    @apply text-red-600 hover:text-red-700 hover:bg-red-50;
  }

  .menu-divider {
    @apply h-px bg-slate-200 mx-2 my-1;
  }

  /* Mobile optimizations */
  @media (max-width: 768px) {
    .menu-dropdown {
      @apply min-w-44 right-0;
    }

    .menu-item {
      @apply py-3.5 text-base;
    }
  }

  /* Gentle pulse animation for better discoverability */
  @keyframes gentle-pulse {
    0%, 100% {
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05), 0 0 0 0 rgba(100, 116, 139, 0);
    }
    50% {
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05), 0 0 0 4px rgba(100, 116, 139, 0.1);
    }
  }
</style>
