# 任务提醒和重复任务系统

## 🎯 功能概述

我们已经成功实现了一个完整的任务提醒和重复任务系统，包含以下核心功能：

### ✅ 已实现功能

1. **任务提醒设置**
   - 在创建/编辑任务时可设置提前几天提醒
   - 默认值：3天
   - 支持0-30天范围
   - 设置为0时禁用提醒

2. **重复任务支持**
   - 支持复杂的重复规则（每日、每周、每月、每年）
   - 自动生成重复任务实例（提前3个月）
   - 重复任务组管理（共享recurringGroupId）
   - 模板与实例分离存储

3. **数据库设计**
   - 扩展tasks表：添加reminderDays、recurringGroupId、isRecurringTemplate、recurringInstanceDate字段
   - 新增task_reminders表：管理提醒通知
   - 完整的索引优化

4. **显示逻辑**
   - 任务列表：只显示实例，不显示模板
   - Calendar页面：显示所有重复任务实例
   - 重复任务在Calendar中按日期正确分布

5. **操作逻辑**
   - 删除重复任务：删除整个组的所有实例
   - 完成任务：只标记特定实例
   - 编辑：更新模板（未来可扩展重新生成实例）

6. **邮件提醒系统**
   - 每日6AM发送提醒邮件
   - 包含即将到期和逾期任务
   - HTML邮件模板
   - 邮件发送日志记录

## 🏗️ 技术架构

### 数据库Schema

```sql
-- 扩展tasks表
ALTER TABLE tasks ADD COLUMN reminder_days integer DEFAULT 3;
ALTER TABLE tasks ADD COLUMN recurring_group_id uuid;
ALTER TABLE tasks ADD COLUMN is_recurring_template boolean DEFAULT false;
ALTER TABLE tasks ADD COLUMN recurring_instance_date timestamp;

-- 新增task_reminders表
CREATE TABLE task_reminders (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES users(id),
  task_id uuid NOT NULL REFERENCES tasks(id),
  reminder_date timestamp NOT NULL,
  reminder_type text NOT NULL,
  sent boolean DEFAULT false,
  sent_at timestamp,
  created_at timestamp DEFAULT now()
);
```

### 核心服务

1. **recurringTasks.ts** - 重复任务管理
   - `generateRecurringInstances()` - 生成重复实例
   - `createRecurringTask()` - 创建重复任务
   - `deleteRecurringTaskGroup()` - 删除重复任务组
   - `generateTaskReminders()` - 生成提醒

2. **emailReminders.ts** - 邮件提醒服务
   - `sendDailyReminders()` - 发送每日提醒
   - `generateReminderEmailHTML()` - 生成邮件HTML
   - `processReminderNotifications()` - 处理特定提醒

### API端点

- `POST /api/tasks` - 创建任务（支持重复任务）
- `PUT /api/tasks/[id]` - 更新任务
- `DELETE /api/tasks/[id]` - 删除任务（支持删除重复任务组）
- `POST /api/cron/daily-reminders` - 定时任务端点

## 🎨 用户界面

### TaskForm组件更新
- 添加提醒设置UI
- 美观的数字输入框
- 响应式设计（桌面/移动端）

### 提醒设置界面
```
Reminder: Remind me [3] days before due date
```

## 📧 邮件提醒示例

用户将收到包含以下内容的HTML邮件：
- 逾期任务（3天内）
- 即将到期任务（根据用户设置）
- 任务优先级标识
- 直接链接到任务管理页面

## 🔄 重复任务示例

### 创建重复任务
1. 用户创建"每月第二个星期二开会"
2. 系统创建模板任务（isRecurringTemplate=true）
3. 自动生成未来3个月的实例
4. 所有实例共享同一个recurringGroupId

### 删除重复任务
- 删除任何一个实例 → 删除整个重复任务组
- 用户会看到"Recurring task group deleted successfully"

### Calendar显示
- 每个重复实例在对应日期显示
- 颜色根据优先级区分
- 点击可查看/编辑特定实例

## 🚀 部署和配置

### 环境变量
```env
CRON_SECRET=your-secret-key  # 用于保护定时任务端点
PUBLIC_APP_URL=https://your-domain.com  # 邮件中的链接
```

### 定时任务设置
建议使用cron job或云服务定时调用：
```bash
# 每天早上6点执行
0 6 * * * curl -X POST -H "Authorization: Bearer YOUR_CRON_SECRET" https://your-domain.com/api/cron/daily-reminders
```

## 🧪 测试

### 开发环境测试
```bash
# 测试邮件提醒系统
curl "http://localhost:3001/api/cron/daily-reminders?secret=dev-secret"
```

### 功能测试清单
- [ ] 创建普通任务（带提醒设置）
- [ ] 创建重复任务
- [ ] 验证Calendar显示重复实例
- [ ] 测试删除重复任务组
- [ ] 测试邮件提醒生成

## 🔮 未来扩展

1. **实际邮件发送**
   - 集成SendGrid/AWS SES
   - 邮件模板自定义

2. **高级重复规则**
   - 工作日重复
   - 节假日跳过
   - 自定义结束条件

3. **提醒增强**
   - 多种提醒方式（邮件、推送、短信）
   - 自定义提醒时间
   - 重复提醒

4. **任务依赖**
   - 任务链
   - 条件触发

5. **团队协作**
   - 共享重复任务
   - 任务分配

## 📝 注意事项

1. **性能考虑**
   - 重复任务实例只生成3个月
   - 需要定期清理过期提醒
   - 考虑分页加载大量重复实例

2. **数据一致性**
   - 删除重复任务组时确保清理所有相关数据
   - 提醒生成的幂等性

3. **用户体验**
   - 重复任务的编辑逻辑需要明确
   - 删除确认对话框
   - 清晰的重复任务标识

这个系统为Routine Mail提供了强大的任务管理和提醒功能，满足了用户对重复任务和及时提醒的需求。
