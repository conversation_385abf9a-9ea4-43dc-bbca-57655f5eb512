# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/routine_mail"

# JWT Secret for authentication
JWT_SECRET="your-super-secret-jwt-key-here"

# SMTP Configuration for email sending
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"

# Application URL (used in emails)
PUBLIC_APP_URL="http://localhost:5173"

# Email Reminder Configuration
EMAIL_REMINDER_CRON="0 8 * * *"  # Daily at 8:00 AM
SCHEDULER_MAINTENANCE_CRON="0 */6 * * *"  # Every 6 hours
CRON_TIMEZONE="Asia/Kuala_Lumpur"

# Scheduler Configuration
SCHEDULER_MAINTENANCE_INTERVAL_HOURS="6"
SCHEDULER_INITIAL_DELAY_SECONDS="5"
RECURRING_GENERATION_YEARS="2"

# Admin Configuration
ADMIN_EMAIL="<EMAIL>"

# Development/Production
NODE_ENV="development"
