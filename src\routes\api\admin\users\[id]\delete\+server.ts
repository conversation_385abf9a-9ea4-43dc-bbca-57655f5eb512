import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { requireAdmin } from '$lib/server/admin.js';
import { deleteUser, getUserById } from '$lib/server/db/operations.js';

export const DELETE: RequestHandler = async (event) => {
  try {
    await requireAdmin(event);

    const { id } = event.params;

    if (!id) {
      return json({ error: 'User ID is required' }, { status: 400 });
    }

    // Check if user exists
    const user = await getUserById(id);
    if (!user) {
      return json({ error: 'User not found' }, { status: 404 });
    }

    // Prevent deleting admin users
    if (user.isAdmin) {
      return json({ error: 'Cannot delete admin users' }, { status: 400 });
    }

    await deleteUser(id);

    return json({ message: 'User deleted successfully' });
  } catch (error) {
    console.error('Admin delete user error:', error);
    return json({ error: 'Unauthorized' }, { status: 401 });
  }
};
