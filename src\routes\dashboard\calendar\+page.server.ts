import { redirect } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';
import { getTasksForCalendar, getCategoriesByUserId } from '$lib/server/db/operations.js';

export const load: PageServerLoad = async ({ locals }) => {
  if (!locals.user) {
    throw redirect(302, '/login');
  }

  try {
    const [tasks, categories] = await Promise.all([
      getTasksForCalendar(locals.user.id),
      getCategoriesByUserId(locals.user.id)
    ]);

    return {
      tasks,
      categories
    };
  } catch (error) {
    console.error('Load calendar page error:', error);
    return {
      tasks: [],
      categories: []
    };
  }
};
