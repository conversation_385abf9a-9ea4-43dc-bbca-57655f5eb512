import { sveltekit } from '@sveltejs/kit/vite';
import { defineConfig } from 'vite';

export default defineConfig({
	plugins: [
		sveltekit(),
		{
			name: 'startup-initialization',
			configureServer(server) {
				// Import and run startup initialization when dev server starts
				import('./src/lib/server/startup.js').then(module => {
					module.startupInitialization();
				}).catch(console.error);
			}
		}
	],
	server: {
		port: 3000
	}
});
