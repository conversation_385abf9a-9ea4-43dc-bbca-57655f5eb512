import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { verifyJWT } from '$lib/server/auth.js';
import { taskScheduler } from '$lib/server/scheduler.js';

export const POST: RequestHandler = async ({ request, cookies }) => {
  try {
    const token = cookies.get('auth-token');
    if (!token) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    const payload = verifyJWT(token);
    if (!payload) {
      return json({ error: 'Invalid token' }, { status: 401 });
    }

    const { action } = await request.json();

    switch (action) {
      case 'maintain':
        await taskScheduler.runMaintenance();
        return json({ message: 'Maintenance completed successfully' });

      case 'status':
        const status = taskScheduler.getStatus();
        return json({ status });

      default:
        return json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error) {
    console.error('Maintenance API error:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
};
