# Task Scheduler Documentation

## Overview

The Routine Mail application now includes an integrated task scheduler using `node-cron` for managing recurring tasks and email reminders. This replaces the previous simple `setInterval` implementation with a more robust and flexible cron-based system.

## Features

### 🔄 Recurring Task Maintenance
- Automatically generates recurring task instances
- Maintains instances up to 3 months ahead
- Configurable generation period (default: 10 years)
- Runs every 6 hours by default

### 📧 Email Reminders
- Daily email reminders for upcoming and overdue tasks
- Specific reminder notifications
- Configurable reminder timing
- Runs daily at 8:00 AM by default

### 🎛️ Admin Management
- Web-based scheduler control panel
- Real-time status monitoring
- Manual trigger capabilities
- Cron job configuration display

## Configuration

### Environment Variables

```bash
# Cron Expressions (Primary Configuration)
SCHEDULER_MAINTENANCE_CRON=0 */6 * * *     # Every 6 hours
EMAIL_REMINDER_CRON=0 8 * * *              # Daily at 8:00 AM
CRON_TIMEZONE=Asia/Kuala_Lumpur            # Timezone for cron jobs

# Legacy Configuration (Backward Compatibility)
SCHEDULER_MAINTENANCE_INTERVAL_HOURS=6     # Hours between maintenance
SCHEDULER_INITIAL_DELAY_SECONDS=5          # Startup delay
EMAIL_REMINDER_HOUR=8                      # Hour for reminders (0-23)
EMAIL_REMINDER_MINUTE=0                    # Minute for reminders (0-59)

# Task Configuration
DEFAULT_REMINDER_DAYS=3                    # Days before due date for reminders
RECURRING_GENERATION_YEARS=10              # Years ahead to generate instances
RECURRING_MAINTENANCE_MONTHS=3             # Months ahead to maintain

# Security
CRON_SECRET=your-secret-key                # Secret for external cron calls
```

### Cron Expression Examples

```bash
# Every 6 hours
0 */6 * * *

# Daily at 8:00 AM
0 8 * * *

# Every 30 minutes
*/30 * * * *

# Weekly on Monday at 9:00 AM
0 9 * * 1

# Monthly on the 1st at midnight
0 0 1 * *
```

## API Endpoints

### Scheduler Management (Admin Only)

#### Get Scheduler Status
```http
GET /api/admin/scheduler
Authorization: Bearer <admin-token>
```

Response:
```json
{
  "success": true,
  "data": {
    "scheduler": {
      "isRunning": true,
      "cronJobCount": 2,
      "cronJobs": ["maintenance", "emailReminders"]
    },
    "cronJobDetails": [
      {
        "name": "maintenance",
        "running": true,
        "expression": "0 */6 * * *"
      },
      {
        "name": "emailReminders",
        "running": true,
        "expression": "0 8 * * *"
      }
    ],
    "configuration": {
      "maintenanceCronExpression": "0 */6 * * *",
      "emailReminderCronExpression": "0 8 * * *",
      "cronTimezone": "Asia/Kuala_Lumpur"
    }
  }
}
```

#### Control Scheduler
```http
POST /api/admin/scheduler
Authorization: Bearer <admin-token>
Content-Type: application/json

{
  "action": "start|stop|restart|runMaintenance|runEmailReminders"
}
```

### Legacy Cron Endpoints (External)

#### Daily Reminders
```http
POST /api/cron/daily-reminders
Authorization: Bearer <cron-secret>
```

## Admin Interface

### Accessing the Scheduler Management

1. Log in as an admin user
2. Navigate to Admin Panel (`/dashboard/admin`)
3. Click "Scheduler Management" button
4. Or directly visit `/admin/scheduler`

### Available Actions

- **Start/Stop/Restart Scheduler**: Control the entire scheduler
- **Run Maintenance Now**: Manually trigger recurring task maintenance
- **Run Email Reminders Now**: Manually trigger email reminder processing
- **Real-time Status**: View current scheduler and cron job status
- **Configuration Display**: See current cron expressions and settings

## Technical Implementation

### Architecture

```
TaskScheduler Class
├── Cron Jobs Map
│   ├── maintenance (recurring tasks)
│   └── emailReminders (daily emails)
├── Configuration Management
├── Status Monitoring
└── Manual Triggers
```

### Key Components

1. **TaskScheduler** (`src/lib/server/scheduler.ts`)
   - Main scheduler class using node-cron
   - Manages multiple cron jobs
   - Provides status and control methods

2. **Configuration** (`src/lib/server/config/scheduler.ts`)
   - Environment variable management
   - Cron expression validation
   - Backward compatibility support

3. **Admin API** (`src/routes/api/admin/scheduler/+server.ts`)
   - RESTful scheduler control
   - Admin authentication required
   - Status reporting and actions

4. **Admin UI** (`src/routes/admin/scheduler/+page.svelte`)
   - Web-based management interface
   - Real-time status display
   - Manual control buttons

### Startup Process

1. Application starts (`src/lib/server/startup.ts`)
2. Scheduler initializes with configuration
3. Cron jobs are created and started
4. Initial maintenance runs after delay
5. Regular cron schedule takes over

## Migration from Previous System

### What Changed

- ✅ Replaced `setInterval` with `node-cron`
- ✅ Added cron expression configuration
- ✅ Enhanced admin management interface
- ✅ Improved error handling and logging
- ✅ Added timezone support
- ✅ Maintained backward compatibility

### Backward Compatibility

The system maintains compatibility with existing environment variables:
- `SCHEDULER_MAINTENANCE_INTERVAL_HOURS` → converted to cron expression
- `EMAIL_REMINDER_HOUR/MINUTE` → converted to cron expression
- External cron endpoints still work

## Troubleshooting

### Common Issues

1. **Scheduler Not Starting**
   - Check environment variables
   - Verify cron expression syntax
   - Check application logs

2. **Cron Jobs Not Running**
   - Verify timezone configuration
   - Check cron expression validity
   - Monitor admin interface

3. **Email Reminders Not Sent**
   - Check SMTP configuration
   - Verify email reminder cron schedule
   - Check user timezone settings

### Logs

Monitor application logs for scheduler activity:
```
✅ Started cron job: maintenance
✅ Started cron job: emailReminders
Running scheduled recurring instances maintenance...
```

### Manual Testing

Use the admin interface to manually trigger operations:
- Run maintenance to test recurring task generation
- Run email reminders to test notification system
- Check status to verify cron job health

## Best Practices

1. **Production Configuration**
   - Use strong `CRON_SECRET`
   - Set appropriate timezone
   - Monitor scheduler status regularly

2. **Cron Expression Design**
   - Avoid overlapping schedules
   - Consider server load timing
   - Test expressions before deployment

3. **Monitoring**
   - Regular admin interface checks
   - Log monitoring for errors
   - Performance impact assessment

## Future Enhancements

- [ ] Cron job history and logs
- [ ] Custom cron expression editor
- [ ] Email notification for scheduler failures
- [ ] Metrics and performance monitoring
- [ ] Multiple timezone support for users
