<!-- REGISTRATION DISABLED - Admin creates users manually -->
<script lang="ts">
  import { goto } from '$app/navigation';
  import { onMount } from 'svelte';

  // Redirect to login page immediately
  onMount(() => {
    goto('/login');
  });
</script>

<svelte:head>
  <title>Registration Disabled - Routine Mail</title>
</svelte:head>

<div class="auth-container">
  <div class="auth-card">
    <div class="auth-header">
      <div class="logo">Routine Mail</div>
      <h1>Registration Disabled</h1>
      <p>User registration is disabled. Please contact your administrator for account access.</p>
    </div>
    <div class="auth-footer">
      <p><a href="/login">Back to Login</a></p>
    </div>
  </div>
</div>

<style>
  .auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 1rem;
  }

  .auth-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    padding: 2rem;
    width: 100%;
    max-width: 400px;
    text-align: center;
  }

  .auth-header {
    margin-bottom: 2rem;
  }

  .logo {
    font-size: 1.5rem;
    font-weight: 700;
    color: #4299e1;
    margin-bottom: 1rem;
  }

  h1 {
    font-size: 1.75rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.5rem;
  }

  p {
    color: #718096;
    line-height: 1.5;
  }

  .auth-footer a {
    color: #4299e1;
    text-decoration: none;
    font-weight: 500;
  }

  .auth-footer a:hover {
    text-decoration: underline;
  }
</style>
