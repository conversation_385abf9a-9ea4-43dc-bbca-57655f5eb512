import { config } from 'dotenv';
import { sendDailyReminders, getTasksForEmailReminder } from '../src/lib/server/services/emailReminders.js';
import { db } from '../src/lib/server/db/index.js';
import { users, tasks } from '../src/lib/server/db/schema.js';
import { eq } from 'drizzle-orm';

// Load environment variables
config({ path: '.env' });

async function testEmailReminders() {
  console.log('🧪 Testing Email Reminder System...\n');

  try {
    // Get all users
    const allUsers = await db.select().from(users);
    console.log(`📊 Found ${allUsers.length} users in database`);

    if (allUsers.length === 0) {
      console.log('❌ No users found. Please create some users first.');
      return;
    }

    // Test each user's reminder data
    for (const user of allUsers) {
      console.log(`\n👤 Testing user: ${user.email}`);
      
      // Get user's tasks
      const userTasks = await db.select()
        .from(tasks)
        .where(eq(tasks.userId, user.id));
      
      console.log(`   📋 User has ${userTasks.length} total tasks`);
      
      // Get reminder data
      const reminderData = await getTasksForEmailReminder(user.id, user.timezone || 'Asia/Kuala_Lumpur');
      
      if (reminderData) {
        const { upcomingTasks, overdueTasks } = reminderData;
        console.log(`   📅 Upcoming tasks: ${upcomingTasks.length}`);
        console.log(`   🚨 Overdue tasks: ${overdueTasks.length}`);
        
        // Show task details
        if (upcomingTasks.length > 0) {
          console.log('   📅 Upcoming tasks:');
          upcomingTasks.forEach(task => {
            console.log(`      - ${task.title} (Due: ${task.dueDate}, Reminder: ${task.reminderDays} days)`);
          });
        }
        
        if (overdueTasks.length > 0) {
          console.log('   🚨 Overdue tasks:');
          overdueTasks.forEach(task => {
            console.log(`      - ${task.title} (Due: ${task.dueDate})`);
          });
        }
        
        if (upcomingTasks.length === 0 && overdueTasks.length === 0) {
          console.log('   ✅ No tasks need reminders for this user');
        }
      } else {
        console.log('   ❌ Failed to get reminder data for user');
      }
    }

    console.log('\n📧 Testing email sending process...');
    
    // Test the actual email sending (this will send real emails if SMTP is configured)
    const shouldSendRealEmails = process.argv.includes('--send-emails');
    
    if (shouldSendRealEmails) {
      console.log('⚠️  Sending real emails...');
      await sendDailyReminders();
    } else {
      console.log('ℹ️  Skipping real email sending. Use --send-emails flag to send actual emails.');
      console.log('   This would call sendDailyReminders() which processes all users.');
    }

    console.log('\n✅ Email reminder system test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    process.exit(0);
  }
}

// Run the test
testEmailReminders();
