# Scheduler Configuration Guide

This document explains all the scheduler-related environment variables that can be configured in the `.env` file.

## Environment Variables

### Task Scheduler Configuration

#### `SCHEDULER_MAINTENANCE_INTERVAL_HOURS`
- **Default**: `6`
- **Description**: How often (in hours) to run maintenance tasks that generate new recurring task instances
- **Example**: `SCHEDULER_MAINTENANCE_INTERVAL_HOURS=12` (run every 12 hours)



#### `SCHEDULER_INITIAL_DELAY_SECONDS`
- **Default**: `5`
- **Description**: How long to wait (in seconds) after server startup before running the first maintenance task
- **Example**: `SCHEDULER_INITIAL_DELAY_SECONDS=30` (wait 30 seconds)

### Email Reminder Configuration

#### `EMAIL_REMINDER_HOUR`
- **Default**: `6`
- **Range**: `0-23`
- **Description**: Hour of the day (24-hour format) when reminder emails should be sent
- **Example**: `EMAIL_REMINDER_HOUR=9` (send at 9 AM)

#### `EMAIL_REMINDER_MINUTE`
- **Default**: `0`
- **Range**: `0-59`
- **Description**: Minute of the hour when reminder emails should be sent
- **Example**: `EMAIL_REMINDER_MINUTE=30` (send at 30 minutes past the hour)

#### `DEFAULT_REMINDER_DAYS`
- **Default**: `3`
- **Description**: Default number of days before a task's due date to send reminder emails
- **Example**: `DEFAULT_REMINDER_DAYS=7` (remind 1 week before due date)

### Recurring Tasks Configuration

#### `RECURRING_GENERATION_YEARS`
- **Default**: `10`
- **Description**: How many years ahead to generate recurring task instances when creating a new recurring task
- **Example**: `RECURRING_GENERATION_YEARS=5` (generate 5 years of instances)

#### `RECURRING_MAINTENANCE_MONTHS`
- **Default**: `3`
- **Description**: How many months ahead to maintain recurring task instances during maintenance runs
- **Example**: `RECURRING_MAINTENANCE_MONTHS=6` (always keep 6 months of future instances)



### Cron Job Configuration

#### `CRON_SECRET`
- **Default**: `dev-secret-change-in-production`
- **Description**: Secret key used to authenticate external cron job requests
- **Example**: `CRON_SECRET=my-super-secret-cron-key-2024`

## Configuration Examples

### High-Frequency Setup (for testing)
```env
SCHEDULER_MAINTENANCE_INTERVAL_HOURS=1
SCHEDULER_INITIAL_DELAY_SECONDS=10
EMAIL_REMINDER_HOUR=8
EMAIL_REMINDER_MINUTE=0
DEFAULT_REMINDER_DAYS=1
RECURRING_GENERATION_YEARS=2
RECURRING_MAINTENANCE_MONTHS=1
```

### Production Setup (recommended)
```env
SCHEDULER_MAINTENANCE_INTERVAL_HOURS=6
SCHEDULER_INITIAL_DELAY_SECONDS=5
EMAIL_REMINDER_HOUR=6
EMAIL_REMINDER_MINUTE=0
DEFAULT_REMINDER_DAYS=3
RECURRING_GENERATION_YEARS=10
RECURRING_MAINTENANCE_MONTHS=3
```

### Low-Resource Setup
```env
SCHEDULER_MAINTENANCE_INTERVAL_HOURS=12
SCHEDULER_INITIAL_DELAY_SECONDS=30
EMAIL_REMINDER_HOUR=6
EMAIL_REMINDER_MINUTE=0
DEFAULT_REMINDER_DAYS=3
RECURRING_GENERATION_YEARS=5
RECURRING_MAINTENANCE_MONTHS=2
```

## How It Works

1. **Server Startup**: When the server starts, the `startup.ts` module loads all configuration from environment variables
2. **Validation**: The configuration is validated and invalid values are replaced with defaults (with warnings)
3. **Scheduler Start**: The task scheduler starts with the configured intervals
4. **Maintenance**: Recurring task instances are generated and maintained according to the schedule
5. **Email Reminders**: Task reminders are scheduled at the configured time

## Monitoring

The server logs show the current configuration when starting:

```
📋 Scheduler Configuration:
  🔄 Maintenance interval: 6 hours
  🧹 Cleanup interval: 24 hours
  ⏱️  Initial delay: 5 seconds
  📧 Email reminder time: 06:00
  📅 Default reminder days: 3 days
  🔁 Recurring generation: 10 years
  🔧 Recurring maintenance: 3 months
  🗑️  Cleanup retention: 90 days
```

## Troubleshooting

- If you see warnings about invalid configuration values, check your `.env` file for typos
- Changes to `.env` require a server restart to take effect
- Use the development setup for testing, but switch to production values for live deployment
- Monitor server logs to ensure schedulers are running as expected
