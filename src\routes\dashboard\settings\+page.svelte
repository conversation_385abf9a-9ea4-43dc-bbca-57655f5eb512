<script lang="ts">
  import { onMount } from 'svelte';
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  import {
    getOfficeFlowLinkStatus,
    redirectToOfficeFlowLink,
    unlinkOfficeFlowAccount
  } from '$lib/client/oauth2.js';
  import type { LayoutData } from '../$types';
  import type { PageData } from './$types';

  export let data: LayoutData & PageData;

  let linkStatus: {
    isLinked: boolean;
    officeFlowUser?: {
      id: string;
      email: string;
      name: string;
      avatar?: string;
      department?: string;
      position?: string;
    };
    linkedAt?: string;
    lastUpdated?: string;
  } = { isLinked: false };

  let loading = false;
  let unlinkLoading = false;
  let message = '';
  let error = '';

  // Local state for email reminders
  let emailRemindersEnabled = data.user.emailRemindersEnabled;

  // Password change state
  let showPasswordForm = false;
  let currentPassword = '';
  let newPassword = '';
  let confirmPassword = '';
  let passwordLoading = false;
  let passwordError = '';

  // Real-time password validation
  $: passwordsMatch = newPassword && confirmPassword && newPassword === confirmPassword;
  $: passwordLengthValid = newPassword.length >= 6;
  $: allFieldsFilled = currentPassword && newPassword && confirmPassword;
  $: canSubmitPassword = allFieldsFilled && passwordsMatch && passwordLengthValid;

  onMount(async () => {
    // Check for success/error messages from URL params
    const urlParams = new URLSearchParams($page.url.search);
    const successParam = urlParams.get('success');
    const errorParam = urlParams.get('error');

    if (successParam === 'account_linked') {
      message = 'Office Flow account linked successfully!';
    } else if (errorParam === 'account_already_linked') {
      error = 'This Office Flow account is already linked to another user.';
    } else if (errorParam) {
      error = 'An error occurred. Please try again.';
    }

    // Load current link status
    await loadLinkStatus();
  });

  async function loadLinkStatus() {
    try {
      linkStatus = await getOfficeFlowLinkStatus();
    } catch (err) {
      console.error('Failed to load link status:', err);
    }
  }

  async function handleLinkAccount() {
    loading = true;
    error = '';
    message = '';

    try {
      await redirectToOfficeFlowLink('/dashboard/settings');
    } catch (err) {
      error = 'Failed to connect to Office Flow. Please try again.';
      loading = false;
    }
  }

  async function handleUnlinkAccount() {
    if (!confirm('Are you sure you want to unlink your Office Flow account? You will no longer be able to sign in using Office Flow.')) {
      return;
    }

    unlinkLoading = true;
    error = '';
    message = '';

    try {
      await unlinkOfficeFlowAccount();
      message = 'Office Flow account unlinked successfully.';
      linkStatus = { isLinked: false };
    } catch (err) {
      error = 'Failed to unlink Office Flow account. Please try again.';
    } finally {
      unlinkLoading = false;
    }
  }

  function clearMessages() {
    message = '';
    error = '';
  }

  async function updateEmailReminders(event: Event) {
    const target = event.target as HTMLInputElement;
    const enabled = target.checked;

    try {
      loading = true;
      const response = await fetch('/api/user/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          emailRemindersEnabled: enabled
        })
      });

      if (!response.ok) {
        throw new Error('Failed to update email reminder settings');
      }

      message = enabled ? 'Email reminders enabled' : 'Email reminders disabled';
      error = '';
      // Update local state
      emailRemindersEnabled = enabled;
    } catch (err) {
      console.error('Update email reminders error:', err);
      error = 'Failed to update email reminder settings';
      message = '';
      // Revert the checkbox state
      emailRemindersEnabled = !enabled;
    } finally {
      loading = false;
    }
  }

  async function handleChangePassword() {
    // Clear previous errors
    passwordError = '';

    // Validate before submission
    if (!canSubmitPassword) {
      if (!passwordsMatch) {
        passwordError = 'New passwords do not match';
      } else if (!passwordLengthValid) {
        passwordError = 'New password must be at least 6 characters long';
      } else {
        passwordError = 'Please fill in all fields';
      }
      return;
    }

    try {
      passwordLoading = true;
      error = '';
      message = '';

      const response = await fetch('/api/user/change-password', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          currentPassword,
          newPassword
        })
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to change password');
      }

      message = 'Password changed successfully';
      // Reset form
      showPasswordForm = false;
      currentPassword = '';
      newPassword = '';
      confirmPassword = '';
      passwordError = '';
    } catch (err) {
      console.error('Change password error:', err);
      passwordError = err instanceof Error ? err.message : 'Failed to change password';
    } finally {
      passwordLoading = false;
    }
  }

  async function handleLogout() {
    try {
      await fetch('/api/auth/logout', {
        method: 'POST',
      });
      goto('/login');
    } catch (error) {
      console.error('Logout error:', error);
    }
  }
</script>

<svelte:head>
  <title>Settings - Routine Mail</title>
</svelte:head>

<div class="settings-container">
  <div class="settings-header">
    <h1>Account Settings</h1>
    <p>Manage your account preferences and connected services</p>
  </div>

  {#if message}
    <div
      class="success-message"
      on:click={clearMessages}
      on:keydown={(e) => e.key === 'Enter' && clearMessages()}
      role="alert"
      tabindex="0"
    >
      {message}
      <button class="close-btn" aria-label="Close success message">×</button>
    </div>
  {/if}

  {#if error}
    <div
      class="error-message"
      on:click={clearMessages}
      on:keydown={(e) => e.key === 'Enter' && clearMessages()}
      role="alert"
      tabindex="0"
    >
      {error}
      <button class="close-btn" aria-label="Close error message">×</button>
    </div>
  {/if}

  <div class="settings-sections">
    <!-- Account Information -->
    <section class="settings-section">
      <h2>Account Information</h2>
      <div class="account-info">
        <div class="info-item">
          <label for="user-email">Email</label>
          <span id="user-email">{data.user.email}</span>
        </div>
        <div class="info-item">
          <label for="user-name">Name</label>
          <span id="user-name">{data.user.name || 'Not set'}</span>
        </div>
        <div class="info-item">
          <label for="user-status">Account Status</label>
          <span id="user-status" class="status {data.user.isVerified ? 'verified' : 'unverified'}">
            {data.user.isVerified ? 'Verified' : 'Unverified'}
          </span>
        </div>
      </div>
    </section>

    <!-- Password Settings -->
    <section class="settings-section">
      <h2>Password Settings</h2>
      <p class="section-description">
        Change your account password
      </p>

      {#if !showPasswordForm}
        <div class="password-actions">
          <button
            class="change-password-btn"
            on:click={() => showPasswordForm = true}
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m0 0a2 2 0 012 2m-2-2a2 2 0 00-2 2m2-2V5a2 2 0 00-2-2m0 0H9a2 2 0 00-2 2v2m0 0a2 2 0 002 2h6a2 2 0 002-2m0 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v2"></path>
            </svg>
            <span>Change Password</span>
          </button>
        </div>
      {:else}
        <div class="password-form">
          {#if passwordError}
            <div class="password-error-message">
              {passwordError}
            </div>
          {/if}

          <div class="form-group">
            <label for="current-password">Current Password</label>
            <input
              id="current-password"
              type="password"
              bind:value={currentPassword}
              placeholder="Enter your current password"
              disabled={passwordLoading}
              required
            />
          </div>

          <div class="form-group">
            <label for="new-password">New Password</label>
            <input
              id="new-password"
              type="password"
              bind:value={newPassword}
              placeholder="Enter new password (min 6 characters)"
              disabled={passwordLoading}
              required
              class={newPassword && !passwordLengthValid ? 'error' : ''}
            />
            {#if newPassword && !passwordLengthValid}
              <div class="field-error">Password must be at least 6 characters long</div>
            {/if}
          </div>

          <div class="form-group">
            <label for="confirm-password">Confirm New Password</label>
            <input
              id="confirm-password"
              type="password"
              bind:value={confirmPassword}
              placeholder="Confirm new password"
              disabled={passwordLoading}
              required
              class={confirmPassword && !passwordsMatch ? 'error' : confirmPassword && passwordsMatch ? 'success' : ''}
            />
            {#if confirmPassword && !passwordsMatch}
              <div class="field-error">Passwords do not match</div>
            {:else if confirmPassword && passwordsMatch}
              <div class="field-success">Passwords match</div>
            {/if}
          </div>

          <div class="form-actions">
            <button
              class="save-btn"
              on:click={handleChangePassword}
              disabled={passwordLoading || !canSubmitPassword}
              title={!canSubmitPassword ? 'Please fill all fields correctly' : ''}
            >
              {passwordLoading ? 'Changing...' : 'Change Password'}
            </button>
            <button
              class="cancel-btn"
              on:click={() => {
                showPasswordForm = false;
                currentPassword = '';
                newPassword = '';
                confirmPassword = '';
                passwordError = '';
                error = '';
              }}
              disabled={passwordLoading}
            >
              Cancel
            </button>
          </div>
        </div>
      {/if}
    </section>

    <!-- Notification Settings -->
    <section class="settings-section">
      <h2>Notification Settings</h2>
      <div class="notification-settings">
        <div class="setting-item">
          <div class="setting-info">
            <label for="email-reminders">Email Reminders</label>
            <p class="setting-description">Receive daily email reminders for upcoming and overdue tasks</p>
          </div>
          <div class="setting-control">
            <label class="toggle-switch">
              <input
                id="email-reminders"
                type="checkbox"
                bind:checked={emailRemindersEnabled}
                on:change={updateEmailReminders}
                disabled={loading}
              />
              <span class="toggle-slider"></span>
            </label>
          </div>
        </div>
      </div>
    </section>

    <!-- Office Flow Integration -->
    {#if data.features.officeFlow}
      <section class="settings-section">
        <h2>Office Flow Integration</h2>
        <p class="section-description">
          Connect your Office Flow account to enable single sign-on and access additional features.
        </p>

      {#if linkStatus.isLinked}
        <div class="linked-account">
          <div class="account-card">
            <div class="account-avatar">
              {#if linkStatus.officeFlowUser?.avatar}
                <img src={linkStatus.officeFlowUser.avatar} alt="Avatar" />
              {:else}
                <div class="avatar-placeholder">
                  {linkStatus.officeFlowUser?.name?.charAt(0) || 'U'}
                </div>
              {/if}
            </div>
            <div class="account-details">
              <h3>{linkStatus.officeFlowUser?.name || 'Unknown User'}</h3>
              <p class="email">{linkStatus.officeFlowUser?.email}</p>
              {#if linkStatus.officeFlowUser?.department}
                <p class="department">{linkStatus.officeFlowUser.department}</p>
              {/if}
              {#if linkStatus.officeFlowUser?.position}
                <p class="position">{linkStatus.officeFlowUser.position}</p>
              {/if}
              <p class="linked-date">
                Linked on {new Date(linkStatus.linkedAt || '').toLocaleDateString()}
              </p>
            </div>
            <div class="account-actions">
              <button 
                class="unlink-btn" 
                on:click={handleUnlinkAccount}
                disabled={unlinkLoading}
              >
                {unlinkLoading ? 'Unlinking...' : 'Unlink Account'}
              </button>
            </div>
          </div>
        </div>
      {:else}
        <div class="unlinked-account">
          <div class="integration-card">
            <div class="integration-icon">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
              </svg>
            </div>
            <div class="integration-content">
              <h3>Office Flow</h3>
              <p>Connect your Office Flow account to enable single sign-on and sync your profile information.</p>
              <ul class="benefits">
                <li>Sign in with your Office Flow credentials</li>
                <li>Automatic profile synchronization</li>
                <li>Access to team features</li>
              </ul>
            </div>
            <div class="integration-actions">
              <button 
                class="link-btn" 
                on:click={handleLinkAccount}
                disabled={loading}
              >
                {loading ? 'Connecting...' : 'Connect Office Flow'}
              </button>
            </div>
          </div>
        </div>
      {/if}
      </section>
    {/if}

    <!-- Admin Panel -->
    {#if data.user.isAdmin}
      <section class="settings-section">
        <h2>Admin Panel</h2>
        <p class="section-description">
          Manage users and system settings
        </p>
        <div class="admin-actions">
          <button
            class="admin-btn"
            on:click={() => goto('/dashboard/admin')}
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
            </svg>
            <span>Open Admin Panel</span>
          </button>
        </div>
      </section>
    {/if}

    <!-- Mobile Logout Section -->
    <section class="settings-section md:hidden">
      <h2>Account Actions</h2>
      <div class="mobile-logout">
        <button
          class="logout-btn"
          on:click={handleLogout}
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
          </svg>
          <span>Logout</span>
        </button>
      </div>
    </section>
  </div>
</div>

<style>
  .settings-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
  }

  .settings-header {
    margin-bottom: 2rem;
  }

  .settings-header h1 {
    font-size: 2rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.5rem;
  }

  .settings-header p {
    color: #718096;
    font-size: 1rem;
  }

  .success-message, .error-message {
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
    position: relative;
    cursor: pointer;
  }

  .success-message {
    background: #c6f6d5;
    color: #22543d;
    border: 1px solid #9ae6b4;
  }

  .error-message {
    background: #fed7d7;
    color: #c53030;
    border: 1px solid #feb2b2;
  }

  .close-btn {
    position: absolute;
    top: 0.5rem;
    right: 0.75rem;
    background: none;
    border: none;
    font-size: 1.25rem;
    cursor: pointer;
    opacity: 0.7;
  }

  .close-btn:hover {
    opacity: 1;
  }

  .settings-sections {
    display: flex;
    flex-direction: column;
    gap: 2rem;
  }

  .settings-section {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
  }

  .settings-section h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.5rem;
  }

  .section-description {
    color: #718096;
    margin-bottom: 1.5rem;
  }

  .account-info {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f7fafc;
  }

  .info-item:last-child {
    border-bottom: none;
  }

  .info-item label {
    font-weight: 500;
    color: #4a5568;
  }

  .info-item span {
    color: #2d3748;
  }

  .status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
  }

  .status.verified {
    background: #c6f6d5;
    color: #22543d;
  }

  .status.unverified {
    background: #fed7d7;
    color: #c53030;
  }

  .account-card, .integration-card {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    padding: 1.5rem;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    background: #f7fafc;
  }

  .account-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
  }

  .account-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .avatar-placeholder {
    width: 100%;
    height: 100%;
    background: #4299e1;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 600;
  }

  .integration-icon {
    width: 60px;
    height: 60px;
    background: #ebf8ff;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #3182ce;
  }

  .integration-icon svg {
    width: 32px;
    height: 32px;
  }

  .account-details, .integration-content {
    flex: 1;
  }

  .account-details h3, .integration-content h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.25rem;
  }

  .email {
    color: #718096;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
  }

  .department, .position {
    color: #4a5568;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
  }

  .linked-date {
    color: #a0aec0;
    font-size: 0.75rem;
  }

  .integration-content p {
    color: #718096;
    margin-bottom: 1rem;
  }

  .benefits {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .benefits li {
    color: #4a5568;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
    position: relative;
    padding-left: 1rem;
  }

  .benefits li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #48bb78;
    font-weight: 600;
  }

  .link-btn, .unlink-btn {
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
  }

  .link-btn {
    background: #4299e1;
    color: white;
  }

  .link-btn:hover:not(:disabled) {
    background: #3182ce;
    transform: translateY(-1px);
  }

  .unlink-btn {
    background: #e53e3e;
    color: white;
  }

  .unlink-btn:hover:not(:disabled) {
    background: #c53030;
    transform: translateY(-1px);
  }

  /* Notification Settings */
  .notification-settings {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .setting-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.7);
    border: 1px solid rgba(226, 232, 240, 0.8);
    border-radius: 12px;
    backdrop-filter: blur(10px);
  }

  .setting-info {
    flex: 1;
  }

  .setting-info label {
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.5rem;
    display: block;
  }

  .setting-description {
    color: #64748b;
    font-size: 0.875rem;
    margin: 0;
  }

  .setting-control {
    margin-left: 1rem;
  }

  /* Toggle Switch */
  .toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
    cursor: pointer;
  }

  .toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
  }

  .toggle-slider {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #cbd5e0;
    border-radius: 24px;
    transition: all 0.3s ease;
  }

  .toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    border-radius: 50%;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  .toggle-switch input:checked + .toggle-slider {
    background-color: #3b82f6;
  }

  .toggle-switch input:checked + .toggle-slider:before {
    transform: translateX(26px);
  }

  .toggle-switch input:disabled + .toggle-slider {
    opacity: 0.5;
    cursor: not-allowed;
  }

  @media (max-width: 768px) {
    .setting-item {
      flex-direction: column;
      gap: 1rem;
      align-items: flex-start;
    }

    .setting-control {
      margin-left: 0;
    }
  }

  .link-btn:disabled, .unlink-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }

  /* Mobile Logout Styles */
  .mobile-logout {
    display: flex;
    justify-content: center;
    padding: 1rem 0;
  }

  .logout-btn {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.5rem;
    background: #ef4444;
    color: white;
    border: none;
    border-radius: 0.5rem;
    font-weight: 500;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(239, 68, 68, 0.2);
  }

  .logout-btn:hover {
    background: #dc2626;
    box-shadow: 0 4px 8px rgba(239, 68, 68, 0.3);
    transform: translateY(-1px);
  }

  .logout-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(239, 68, 68, 0.2);
  }

  /* Admin Panel Styles */
  .admin-actions {
    display: flex;
    justify-content: center;
    padding: 1rem 0;
  }

  .admin-btn {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.5rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 0.5rem;
    font-weight: 500;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(102, 126, 234, 0.2);
  }

  .admin-btn:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
    box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
    transform: translateY(-1px);
  }

  .admin-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(102, 126, 234, 0.2);
  }

  /* Password Settings */
  .password-actions {
    display: flex;
    justify-content: center;
    padding: 1rem 0;
  }

  .change-password-btn {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.5rem;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    border: none;
    border-radius: 0.5rem;
    font-weight: 500;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
  }

  .change-password-btn:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
    box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
    transform: translateY(-1px);
  }

  .password-form {
    max-width: 400px;
    margin: 0 auto;
  }

  .form-group {
    margin-bottom: 1.5rem;
  }

  .form-group label {
    display: block;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;
  }

  .form-group input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    font-size: 1rem;
    transition: border-color 0.2s ease;
  }

  .form-group input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  .form-group input:disabled {
    background-color: #f9fafb;
    cursor: not-allowed;
  }

  .form-group input.error {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
  }

  .form-group input.success {
    border-color: #10b981;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
  }

  .field-error {
    color: #ef4444;
    font-size: 0.875rem;
    margin-top: 0.25rem;
  }

  .field-success {
    color: #10b981;
    font-size: 0.875rem;
    margin-top: 0.25rem;
  }

  .password-error-message {
    background: #fef2f2;
    color: #dc2626;
    border: 1px solid #fecaca;
    padding: 0.75rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    font-size: 0.875rem;
  }

  .form-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
  }

  .save-btn, .cancel-btn {
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
  }

  .save-btn {
    background: #10b981;
    color: white;
  }

  .save-btn:hover:not(:disabled) {
    background: #059669;
    transform: translateY(-1px);
  }

  .save-btn:disabled {
    background: #9ca3af;
    cursor: not-allowed;
    transform: none;
  }

  .cancel-btn {
    background: #6b7280;
    color: white;
  }

  .cancel-btn:hover:not(:disabled) {
    background: #4b5563;
    transform: translateY(-1px);
  }

  @media (max-width: 768px) {
    .settings-container {
      padding: 1rem;
    }

    .account-card, .integration-card {
      flex-direction: column;
      text-align: center;
      gap: 1rem;
    }

    .info-item {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;
    }

    .form-actions {
      flex-direction: column;
    }

    .password-form {
      max-width: none;
    }
  }
</style>
