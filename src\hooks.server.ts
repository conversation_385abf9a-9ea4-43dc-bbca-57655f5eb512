import type { Handle } from '@sveltejs/kit';
import { verifyJWT } from '$lib/server/auth.js';
import { getSessionByToken, getUserById } from '$lib/server/db/operations.js';

export const handle: Handle = async ({ event, resolve }) => {
  const token = event.cookies.get('auth-token');

  if (token) {
    try {
      // Verify JWT
      const payload = verifyJWT(token);
      if (payload) {
        // Check if session exists in database
        const session = await getSessionByToken(token);
        if (session && session.expiresAt > new Date()) {
          // Get user data
          const user = await getUserById(session.userId);
          if (user) {
            event.locals.user = {
              id: user.id,
              email: user.email,
              name: user.name,
              isVerified: user.isVerified,
              isAdmin: user.isAdmin || false,
              isSuspended: user.isSuspended || false,
              emailRemindersEnabled: user.emailRemindersEnabled,
              timezone: user.timezone,
              emailFrequencyDays: user.emailFrequencyDays,
              emailPreviewDays: user.emailPreviewDays
            };
          }
        }
      }
    } catch (error) {
      console.error('Auth middleware error:', error);
      // Clear invalid token
      event.cookies.delete('auth-token', { path: '/' });
    }
  }

  return resolve(event);
};
