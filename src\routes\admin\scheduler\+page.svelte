<script lang="ts">
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import { page } from '$app/stores';
  import Button from '$lib/components/ui/Button.svelte';
  import Toast from '$lib/components/ui/Toast.svelte';

  interface SchedulerData {
    scheduler: {
      isRunning: boolean;
      cronJobCount: number;
      cronJobs: string[];
    };
    cronJobDetails: Array<{
      name: string;
      running: boolean;
      expression?: string;
    }>;
    configuration: {
      maintenanceCronExpression: string;
      emailReminderCronExpression: string;
      cronTimezone: string;
      initialDelaySeconds: number;
      defaultReminderDays: number;
      recurringGenerationYears: number;
      recurringMaintenanceMonths: number;
    };
  }

  let schedulerData: SchedulerData | null = null;
  let loading = true;
  let actionLoading = false;
  let toast = { show: false, message: '', type: 'success' as 'success' | 'error' };

  onMount(async () => {
    await loadSchedulerData();
  });

  async function loadSchedulerData() {
    try {
      const response = await fetch('/api/admin/scheduler', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.status === 401) {
        goto('/auth/login');
        return;
      }

      const result = await response.json();
      if (result.success) {
        schedulerData = result.data;
      } else {
        showToast('Failed to load scheduler data', 'error');
      }
    } catch (error) {
      console.error('Error loading scheduler data:', error);
      showToast('Failed to load scheduler data', 'error');
    } finally {
      loading = false;
    }
  }

  async function performAction(action: string) {
    actionLoading = true;
    try {
      const response = await fetch('/api/admin/scheduler', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ action })
      });

      const result = await response.json();
      if (result.success) {
        showToast(result.message, 'success');
        // Reload data after action
        setTimeout(() => loadSchedulerData(), 1000);
      } else {
        showToast(result.error || 'Action failed', 'error');
      }
    } catch (error) {
      console.error('Error performing action:', error);
      showToast('Action failed', 'error');
    } finally {
      actionLoading = false;
    }
  }

  function showToast(message: string, type: 'success' | 'error') {
    toast = { show: true, message, type };
    setTimeout(() => {
      toast.show = false;
    }, 3000);
  }

  function getStatusBadge(running: boolean) {
    return running 
      ? 'bg-emerald-100 text-emerald-800 border-emerald-200' 
      : 'bg-red-100 text-red-800 border-red-200';
  }

  function formatCronExpression(expression: string) {
    // Simple cron expression descriptions
    const descriptions: Record<string, string> = {
      '0 */6 * * *': 'Every 6 hours',
      '0 8 * * *': 'Daily at 8:00 AM',
      '0 6 * * *': 'Daily at 6:00 AM',
      '*/5 * * * *': 'Every 5 minutes',
      '0 0 * * *': 'Daily at midnight'
    };
    
    return descriptions[expression] || expression;
  }
</script>

<svelte:head>
  <title>Scheduler Management - Admin</title>
</svelte:head>

<div class="min-h-screen bg-slate-50">
  <div class="max-w-6xl mx-auto px-4 py-8">
    <!-- Header -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-slate-900 mb-2">Scheduler Management</h1>
      <p class="text-slate-600">Monitor and control the task scheduler and cron jobs</p>
    </div>

    {#if loading}
      <div class="flex items-center justify-center py-12">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span class="ml-3 text-slate-600">Loading scheduler data...</span>
      </div>
    {:else if schedulerData}
      <!-- Scheduler Status -->
      <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6 mb-6">
        <h2 class="text-xl font-semibold text-slate-900 mb-4">Scheduler Status</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div class="text-center">
            <div class="text-2xl font-bold text-slate-900">{schedulerData.scheduler.isRunning ? 'Running' : 'Stopped'}</div>
            <div class="text-sm text-slate-600">Status</div>
            <div class="mt-2">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border {getStatusBadge(schedulerData.scheduler.isRunning)}">
                {schedulerData.scheduler.isRunning ? '🟢 Active' : '🔴 Inactive'}
              </span>
            </div>
          </div>
          
          <div class="text-center">
            <div class="text-2xl font-bold text-slate-900">{schedulerData.scheduler.cronJobCount}</div>
            <div class="text-sm text-slate-600">Active Cron Jobs</div>
          </div>
          
          <div class="text-center">
            <div class="text-2xl font-bold text-slate-900">{schedulerData.configuration.cronTimezone}</div>
            <div class="text-sm text-slate-600">Timezone</div>
          </div>
        </div>

        <!-- Control Buttons -->
        <div class="flex flex-wrap gap-3">
          <Button 
            variant="primary" 
            disabled={actionLoading || schedulerData.scheduler.isRunning}
            on:click={() => performAction('start')}
          >
            Start Scheduler
          </Button>
          
          <Button 
            variant="secondary" 
            disabled={actionLoading || !schedulerData.scheduler.isRunning}
            on:click={() => performAction('stop')}
          >
            Stop Scheduler
          </Button>
          
          <Button 
            variant="secondary" 
            disabled={actionLoading}
            on:click={() => performAction('restart')}
          >
            Restart Scheduler
          </Button>
          
          <Button 
            variant="ghost" 
            disabled={actionLoading}
            on:click={() => performAction('runMaintenance')}
          >
            Run Maintenance Now
          </Button>
          
          <Button 
            variant="ghost" 
            disabled={actionLoading}
            on:click={() => performAction('runEmailReminders')}
          >
            Run Email Reminders Now
          </Button>
        </div>
      </div>

      <!-- Cron Jobs Details -->
      <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6 mb-6">
        <h2 class="text-xl font-semibold text-slate-900 mb-4">Cron Jobs</h2>
        
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-slate-200">
            <thead class="bg-slate-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Job Name</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Status</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Cron Expression</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Description</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-slate-200">
              {#each schedulerData.cronJobDetails as job}
                <tr>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-slate-900 capitalize">
                    {job.name}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border {getStatusBadge(job.running)}">
                      {job.running ? 'Running' : 'Stopped'}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-900 font-mono">
                    {job.expression || 'N/A'}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-600">
                    {job.expression ? formatCronExpression(job.expression) : 'No expression'}
                  </td>
                </tr>
              {/each}
            </tbody>
          </table>
        </div>
      </div>

      <!-- Configuration -->
      <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
        <h2 class="text-xl font-semibold text-slate-900 mb-4">Configuration</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 class="text-sm font-medium text-slate-700 mb-2">Maintenance Schedule</h3>
            <p class="text-sm text-slate-600 mb-1">Expression: <code class="bg-slate-100 px-1 rounded">{schedulerData.configuration.maintenanceCronExpression}</code></p>
            <p class="text-sm text-slate-500">{formatCronExpression(schedulerData.configuration.maintenanceCronExpression)}</p>
          </div>
          
          <div>
            <h3 class="text-sm font-medium text-slate-700 mb-2">Email Reminders</h3>
            <p class="text-sm text-slate-600 mb-1">Expression: <code class="bg-slate-100 px-1 rounded">{schedulerData.configuration.emailReminderCronExpression}</code></p>
            <p class="text-sm text-slate-500">{formatCronExpression(schedulerData.configuration.emailReminderCronExpression)}</p>
          </div>
          
          <div>
            <h3 class="text-sm font-medium text-slate-700 mb-2">Recurring Tasks</h3>
            <p class="text-sm text-slate-600">Generation: {schedulerData.configuration.recurringGenerationYears} years ahead</p>
            <p class="text-sm text-slate-600">Maintenance: {schedulerData.configuration.recurringMaintenanceMonths} months ahead</p>
          </div>
          
          <div>
            <h3 class="text-sm font-medium text-slate-700 mb-2">Other Settings</h3>
            <p class="text-sm text-slate-600">Initial delay: {schedulerData.configuration.initialDelaySeconds} seconds</p>
            <p class="text-sm text-slate-600">Default reminder days: {schedulerData.configuration.defaultReminderDays} days</p>
          </div>
        </div>
      </div>
    {:else}
      <div class="text-center py-12">
        <p class="text-slate-600">Failed to load scheduler data</p>
        <Button variant="primary" on:click={loadSchedulerData} class="mt-4">
          Retry
        </Button>
      </div>
    {/if}
  </div>
</div>

{#if toast.show}
  <Toast message={toast.message} type={toast.type} />
{/if}
