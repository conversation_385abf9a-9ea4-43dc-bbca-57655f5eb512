import { db } from '../db/index.js';
import { users, tasks, taskReminders, emailLogs } from '../db/schema.js';
import type { User, Task, TaskReminder } from '../db/schema.js';
import { eq, and, gte, lt, lte, isNotNull } from 'drizzle-orm';
import { getTasksNeedingReminders, markReminderSent } from './recurringTasks.js';
import { config } from 'dotenv';

// Load environment variables
config({ path: '.env' });

/**
 * Generate a secure token for task operations in email
 */
function generateTaskToken(taskId: string, userId: string): string {
  // Simple token generation - in production, use proper JWT or similar
  const payload = `${taskId}:${userId}:${Date.now()}`;
  return Buffer.from(payload).toString('base64url');
}

interface EmailReminderData {
  user: User;
  upcomingTasks: Task[];
  overdueTasks: Task[];
}

/**
 * Get all users who need email reminders today
 */
export async function getUsersNeedingReminders(): Promise<User[]> {
  // Get all verified users who have email reminders enabled
  return await db.select()
    .from(users)
    .where(
      and(
        eq(users.isVerified, true),
        eq(users.emailRemindersEnabled, true)
      )
    );
}

/**
 * Get tasks for email reminder for a specific user based on their reminder settings
 */
export async function getTasksForEmailReminder(userId: string, userTimezone: string = 'Asia/Kuala_Lumpur'): Promise<EmailReminderData | null> {
  const user = await db.select().from(users).where(eq(users.id, userId)).then(rows => rows[0]);
  if (!user) return null;

  const now = new Date();
  const today = new Date(now);
  today.setHours(0, 0, 0, 0);

  // Get all incomplete tasks for this user (excluding templates)
  const allTasks = await db.select()
    .from(tasks)
    .where(
      and(
        eq(tasks.userId, userId),
        eq(tasks.completed, false),
        eq(tasks.isRecurringTemplate, false), // Exclude templates
        isNotNull(tasks.dueDate) // Only tasks with due dates
      )
    );

  const upcomingTasks: Task[] = [];
  const overdueTasks: Task[] = [];

  // Filter tasks based on individual reminder settings
  for (const task of allTasks) {
    if (!task.dueDate) continue;

    const dueDate = new Date(task.dueDate);
    dueDate.setHours(0, 0, 0, 0);

    const daysDiff = Math.ceil((dueDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

    // Use task's reminderDays setting, fallback to 3 days if not set
    const reminderDays = task.reminderDays !== null ? task.reminderDays : 3;

    if (daysDiff < 0) {
      // Task is overdue
      const daysOverdue = Math.abs(daysDiff);
      // Include overdue tasks within 3 days
      if (daysOverdue <= 3) {
        overdueTasks.push(task);
      }
    } else {
      // Task is due in the future or today
      // Include if within the reminder period
      if (daysDiff <= reminderDays) {
        upcomingTasks.push(task);
      }
    }
  }

  return {
    user,
    upcomingTasks,
    overdueTasks
  };
}

/**
 * Generate HTML email content for task reminders
 */
export function generateReminderEmailHTML(data: EmailReminderData): string {
  const { user, upcomingTasks, overdueTasks } = data;

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatPriority = (priority: number) => {
    switch (priority) {
      case 0: return '🔵 Low';
      case 1: return '🟡 Normal';
      case 2: return '🔴 High';
      default: return '🟡 Normal';
    }
  };

  const appUrl = process.env.PUBLIC_APP_URL || 'https://routine-mail.com';

  let html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Task Reminders - Routine Mail</title>
      <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 20px; background-color: #f8fafc; }
        .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); overflow: hidden; }
        .header { background: linear-gradient(135deg, #3b82f6, #1d4ed8); color: white; padding: 32px 24px; text-align: center; }
        .header h1 { margin: 0 0 8px 0; font-size: 28px; font-weight: 700; }
        .header p { margin: 0; font-size: 16px; opacity: 0.9; }
        .content { padding: 32px 24px; }
        .section { margin-bottom: 32px; }
        .section h2 { color: #1f2937; margin: 0 0 16px 0; font-size: 20px; font-weight: 600; display: flex; align-items: center; }
        .task-item { background: #ffffff; border: 1px solid #e5e7eb; border-radius: 8px; padding: 16px; margin-bottom: 12px; transition: all 0.2s; }
        .task-item:hover { box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
        .task-header { display: flex; justify-content: between; align-items: flex-start; margin-bottom: 8px; }
        .task-title { font-weight: 600; color: #1f2937; font-size: 16px; margin-bottom: 4px; }
        .task-title a { color: #1f2937; text-decoration: none; }
        .task-title a:hover { color: #3b82f6; }
        .task-meta { font-size: 14px; color: #6b7280; margin-bottom: 12px; }
        .task-actions { display: flex; gap: 8px; }
        .btn { display: inline-block; padding: 8px 16px; border-radius: 6px; text-decoration: none; font-size: 14px; font-weight: 500; text-align: center; }
        .btn-primary { background: #3b82f6; color: white; }
        .btn-primary:hover { background: #2563eb; }
        .btn-secondary { background: #f3f4f6; color: #374151; border: 1px solid #d1d5db; }
        .btn-secondary:hover { background: #e5e7eb; }
        .overdue { border-left: 4px solid #ef4444; background: #fef2f2; }
        .upcoming { border-left: 4px solid #3b82f6; background: #eff6ff; }
        .priority-high { border-top: 3px solid #ef4444; }
        .priority-normal { border-top: 3px solid #f59e0b; }
        .priority-low { border-top: 3px solid #10b981; }
        .footer { background: #f8fafc; padding: 24px; text-align: center; font-size: 14px; color: #6b7280; }
        .footer a { color: #3b82f6; text-decoration: none; }
        .no-tasks { text-align: center; color: #6b7280; font-style: italic; padding: 40px 20px; }
        .summary { background: #f8fafc; border-radius: 8px; padding: 16px; margin-bottom: 24px; text-align: center; }
        .summary-number { font-size: 24px; font-weight: 700; color: #3b82f6; }
        .summary-text { color: #6b7280; font-size: 14px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>📋 Task Reminders</h1>
          <p>Good morning, ${user.name || user.email}!</p>
        </div>
        <div class="content">
          <div class="summary">
            <div class="summary-number">${upcomingTasks.length + overdueTasks.length}</div>
            <div class="summary-text">task${upcomingTasks.length + overdueTasks.length !== 1 ? 's' : ''} need your attention</div>
          </div>
  `;

  // Overdue tasks section
  if (overdueTasks.length > 0) {
    html += `
      <div class="section">
        <h2>🚨 Overdue Tasks</h2>
    `;

    overdueTasks.forEach(task => {
      const priorityClass = task.priority === 2 ? 'priority-high' : task.priority === 0 ? 'priority-low' : 'priority-normal';
      html += `
        <div class="task-item overdue ${priorityClass}">
          <div class="task-title">
            <a href="${appUrl}/tasks/${task.id}" target="_blank">${task.title}</a>
          </div>
          <div class="task-meta">
            ${formatPriority(task.priority || 1)} • Due: ${task.dueDate ? formatDate(new Date(task.dueDate)) : 'No date'}
            ${task.notes ? `<br><em>${task.notes.substring(0, 100)}${task.notes.length > 100 ? '...' : ''}</em>` : ''}
          </div>
          <div class="task-actions">
            <a href="${appUrl}/tasks/${task.id}" class="btn btn-primary" target="_blank">View Task</a>
            <a href="${appUrl}/api/tasks/${task.id}/complete?token=${generateTaskToken(task.id, user.id)}" class="btn btn-secondary" target="_blank">Mark Complete</a>
          </div>
        </div>
      `;
    });

    html += `</div>`;
  }

  // Upcoming tasks section
  if (upcomingTasks.length > 0) {
    html += `
      <div class="section">
        <h2>📅 Upcoming Tasks</h2>
    `;

    upcomingTasks.forEach(task => {
      const priorityClass = task.priority === 2 ? 'priority-high' : task.priority === 0 ? 'priority-low' : 'priority-normal';
      html += `
        <div class="task-item upcoming ${priorityClass}">
          <div class="task-title">
            <a href="${appUrl}/tasks/${task.id}" target="_blank">${task.title}</a>
          </div>
          <div class="task-meta">
            ${formatPriority(task.priority || 1)} • Due: ${task.dueDate ? formatDate(new Date(task.dueDate)) : 'No date'}
            ${task.notes ? `<br><em>${task.notes.substring(0, 100)}${task.notes.length > 100 ? '...' : ''}</em>` : ''}
          </div>
          <div class="task-actions">
            <a href="${appUrl}/tasks/${task.id}" class="btn btn-primary" target="_blank">View Task</a>
            <a href="${appUrl}/api/tasks/${task.id}/complete?token=${generateTaskToken(task.id, user.id)}" class="btn btn-secondary" target="_blank">Mark Complete</a>
          </div>
        </div>
      `;
    });

    html += `</div>`;
  }

  // No tasks message
  if (upcomingTasks.length === 0 && overdueTasks.length === 0) {
    html += `
      <div class="no-tasks">
        🎉 No urgent tasks today! You're all caught up.
      </div>
    `;
  }

  html += `
        </div>
        <div class="footer">
          <p><strong>Routine Mail</strong> - Your daily task companion</p>
          <p>
            <a href="${appUrl}">Open Routine Mail</a> •
            <a href="${appUrl}/settings">Email Settings</a> •
            <a href="${appUrl}/tasks/new">Add New Task</a>
          </p>
          <p style="font-size: 12px; color: #9ca3af; margin-top: 16px;">
            You're receiving this because you have email reminders enabled.
            <a href="${appUrl}/settings" style="color: #9ca3af;">Manage preferences</a>
          </p>
        </div>
      </div>
    </body>
    </html>
  `;

  return html;
}

/**
 * Send daily reminder emails to all users
 */
export async function sendDailyReminders(): Promise<void> {
  console.log('Starting daily reminder email process...');

  const users = await getUsersNeedingReminders();
  console.log(`Found ${users.length} users to process`);

  for (const user of users) {
    try {
      const reminderData = await getTasksForEmailReminder(user.id, user.timezone || 'Asia/Kuala_Lumpur');

      if (!reminderData) continue;

      const { upcomingTasks, overdueTasks } = reminderData;
      const totalTasks = upcomingTasks.length + overdueTasks.length;

      // Only send email if there are tasks to remind about
      if (totalTasks > 0) {
        const emailHTML = generateReminderEmailHTML(reminderData);

        // Send actual email
        const { sendEmail } = await import('../email.js');
        const emailSent = await sendEmail(
          user.email,
          `Daily Task Reminder - ${totalTasks} task${totalTasks > 1 ? 's' : ''} need your attention`,
          emailHTML
        );

        // Log the email attempt
        await db.insert(emailLogs).values({
          userId: user.id,
          emailType: 'daily_reminder',
          taskCount: totalTasks,
          success: emailSent
        });

        if (emailSent) {
          console.log(`✅ Email sent successfully to ${user.email} with ${totalTasks} tasks`);
        } else {
          console.error(`❌ Failed to send email to ${user.email}`);
        }
      }
    } catch (error) {
      console.error(`Failed to send reminder to ${user.email}:`, error);

      // Log the failed attempt
      await db.insert(emailLogs).values({
        userId: user.id,
        emailType: 'daily_reminder',
        taskCount: 0,
        success: false
      });
    }
  }

  console.log('Daily reminder email process completed');
}

/**
 * Process specific reminder notifications
 */
export async function processReminderNotifications(): Promise<void> {
  const reminders = await getTasksNeedingReminders();

  for (const reminder of reminders) {
    try {
      // Get the task and user details
      const [task] = await db.select().from(tasks).where(eq(tasks.id, reminder.taskId));
      const [user] = await db.select().from(users).where(eq(users.id, reminder.userId));

      if (task && user) {
        // TODO: Send specific reminder email
        console.log(`Sending reminder for task "${task.title}" to ${user.email}`);

        // Mark reminder as sent
        await markReminderSent(reminder.id);
      }
    } catch (error) {
      console.error(`Failed to process reminder ${reminder.id}:`, error);
    }
  }
}
