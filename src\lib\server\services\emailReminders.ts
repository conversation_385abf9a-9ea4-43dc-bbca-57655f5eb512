import { db } from '../db/index.js';
import { users, tasks, taskReminders, emailLogs } from '../db/schema.js';
import type { User, Task, TaskReminder } from '../db/schema.js';
import { eq, and, gte, lt, lte, isNotNull } from 'drizzle-orm';
import { getTasksNeedingReminders, markReminderSent } from './recurringTasks.js';
import { config } from 'dotenv';

// Load environment variables
config({ path: '.env' });

/**
 * Generate a secure token for task operations in email
 */
function generateTaskToken(taskId: string, userId: string): string {
  // Simple token generation - in production, use proper JWT or similar
  const payload = `${taskId}:${userId}:${Date.now()}`;
  return Buffer.from(payload).toString('base64url');
}

interface EmailReminderData {
  user: User;
  upcomingTasks: Task[];
  overdueTasks: Task[];
}

/**
 * Get all users who need email reminders today
 */
export async function getUsersNeedingReminders(): Promise<User[]> {
  // Get all verified users who have email reminders enabled
  return await db.select()
    .from(users)
    .where(
      and(
        eq(users.isVerified, true),
        eq(users.emailRemindersEnabled, true)
      )
    );
}

/**
 * Get tasks for email reminder for a specific user based on their reminder settings
 */
export async function getTasksForEmailReminder(userId: string, userTimezone: string = 'Asia/Kuala_Lumpur'): Promise<EmailReminderData | null> {
  const user = await db.select().from(users).where(eq(users.id, userId)).then(rows => rows[0]);
  if (!user) return null;

  const now = new Date();
  const today = new Date(now);
  today.setHours(0, 0, 0, 0);

  // Get all incomplete tasks for this user (excluding templates)
  const allTasks = await db.select()
    .from(tasks)
    .where(
      and(
        eq(tasks.userId, userId),
        eq(tasks.completed, false),
        eq(tasks.isRecurringTemplate, false), // Exclude templates
        isNotNull(tasks.dueDate) // Only tasks with due dates
      )
    );

  const upcomingTasks: Task[] = [];
  const overdueTasks: Task[] = [];

  // Filter tasks based on individual reminder settings
  for (const task of allTasks) {
    if (!task.dueDate) continue;

    const dueDate = new Date(task.dueDate);
    dueDate.setHours(0, 0, 0, 0);

    const daysDiff = Math.ceil((dueDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

    // Use task's reminderDays setting, fallback to 3 days if not set
    const reminderDays = task.reminderDays !== null ? task.reminderDays : 3;

    if (daysDiff < 0) {
      // Task is overdue
      const daysOverdue = Math.abs(daysDiff);
      // Include overdue tasks within 3 days
      if (daysOverdue <= 3) {
        overdueTasks.push(task);
      }
    } else {
      // Task is due in the future or today
      // Include if within the reminder period
      if (daysDiff <= reminderDays) {
        upcomingTasks.push(task);
      }
    }
  }

  return {
    user,
    upcomingTasks,
    overdueTasks
  };
}

/**
 * Generate HTML email content for task reminders
 */
export function generateReminderEmailHTML(data: EmailReminderData): string {
  const { user, upcomingTasks, overdueTasks } = data;

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatPriority = (priority: number) => {
    switch (priority) {
      case 0: return '🔵 Low';
      case 1: return '🟡 Normal';
      case 2: return '🔴 High';
      default: return '🟡 Normal';
    }
  };

  const appUrl = process.env.PUBLIC_APP_URL || 'http://localhost:5173';

  let html = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta http-equiv="X-UA-Compatible" content="IE=edge">
      <title>Daily Task Reminders - Routine Mail</title>
      <style>
        /* Reset and base styles */
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
          line-height: 1.6;
          color: #1a202c;
          margin: 0;
          padding: 0;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          min-height: 100vh;
        }

        /* Main container */
        .email-wrapper {
          width: 100%;
          padding: 40px 20px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .container {
          max-width: 600px;
          margin: 0 auto;
          background: #ffffff;
          border-radius: 16px;
          box-shadow: 0 20px 40px rgba(0,0,0,0.1);
          overflow: hidden;
        }

        /* Header */
        .header {
          background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
          color: white;
          padding: 40px 32px;
          text-align: center;
          position: relative;
        }
        .header::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
          opacity: 0.3;
        }
        .header-content { position: relative; z-index: 1; }
        .header h1 {
          margin: 0 0 12px 0;
          font-size: 32px;
          font-weight: 800;
          letter-spacing: -0.5px;
        }
        .header p {
          margin: 0;
          font-size: 18px;
          opacity: 0.9;
          font-weight: 400;
        }

        /* Content area */
        .content { padding: 40px 32px; }

        /* Summary card */
        .summary {
          background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
          border-radius: 12px;
          padding: 24px;
          margin-bottom: 32px;
          text-align: center;
          border: 1px solid #e2e8f0;
        }
        .summary-number {
          font-size: 36px;
          font-weight: 900;
          background: linear-gradient(135deg, #4f46e5, #7c3aed);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
          margin-bottom: 8px;
        }
        .summary-text {
          color: #64748b;
          font-size: 16px;
          font-weight: 500;
        }

        /* Section headers */
        .section { margin-bottom: 32px; }
        .section h2 {
          color: #1e293b;
          margin: 0 0 20px 0;
          font-size: 22px;
          font-weight: 700;
          display: flex;
          align-items: center;
          gap: 8px;
        }

        /* Task items */
        .task-item {
          background: #ffffff;
          border: 1px solid #e2e8f0;
          border-radius: 12px;
          padding: 20px;
          margin-bottom: 16px;
          transition: all 0.3s ease;
          box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .task-item:hover {
          box-shadow: 0 8px 25px rgba(0,0,0,0.1);
          transform: translateY(-2px);
        }

        .task-title {
          font-weight: 600;
          color: #1e293b;
          font-size: 18px;
          margin-bottom: 8px;
          line-height: 1.4;
        }
        .task-title a {
          color: #1e293b;
          text-decoration: none;
        }
        .task-title a:hover {
          color: #4f46e5;
        }

        .task-meta {
          font-size: 14px;
          color: #64748b;
          margin-bottom: 16px;
          line-height: 1.5;
        }

        .task-actions {
          display: flex;
          gap: 12px;
          flex-wrap: wrap;
        }

        /* Buttons */
        .btn {
          display: inline-block;
          padding: 12px 20px;
          border-radius: 8px;
          text-decoration: none;
          font-size: 14px;
          font-weight: 600;
          text-align: center;
          transition: all 0.2s ease;
          border: none;
        }
        .btn-primary {
          background: linear-gradient(135deg, #4f46e5, #7c3aed);
          color: white;
          box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
        }
        .btn-primary:hover {
          transform: translateY(-1px);
          box-shadow: 0 6px 16px rgba(79, 70, 229, 0.4);
        }
        .btn-secondary {
          background: #f8fafc;
          color: #475569;
          border: 1px solid #e2e8f0;
        }
        .btn-secondary:hover {
          background: #f1f5f9;
          border-color: #cbd5e1;
        }

        /* Task status styles */
        .overdue {
          border-left: 4px solid #ef4444;
          background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
        }
        .upcoming {
          border-left: 4px solid #3b82f6;
          background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
        }

        /* Priority indicators */
        .priority-high { border-top: 3px solid #ef4444; }
        .priority-normal { border-top: 3px solid #f59e0b; }
        .priority-low { border-top: 3px solid #10b981; }

        /* Footer */
        .footer {
          background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
          padding: 32px;
          text-align: center;
          font-size: 14px;
          color: #64748b;
          border-top: 1px solid #e2e8f0;
        }
        .footer a {
          color: #4f46e5;
          text-decoration: none;
          font-weight: 500;
        }
        .footer a:hover {
          text-decoration: underline;
        }

        /* No tasks message */
        .no-tasks {
          text-align: center;
          color: #64748b;
          font-style: italic;
          padding: 60px 20px;
          font-size: 18px;
        }

        /* Responsive design */
        @media only screen and (max-width: 600px) {
          .email-wrapper { padding: 20px 10px; }
          .content { padding: 24px 20px; }
          .header { padding: 32px 20px; }
          .header h1 { font-size: 28px; }
          .task-actions { flex-direction: column; }
          .btn { width: 100%; }
        }
      </style>
    </head>
    <body>
      <div class="email-wrapper">
        <div class="container">
          <div class="header">
            <div class="header-content">
              <h1>📋 Daily Task Reminders</h1>
              <p>Good morning, ${user.name || user.email}!</p>
            </div>
          </div>
          <div class="content">
            <div class="summary">
              <div class="summary-number">${upcomingTasks.length + overdueTasks.length}</div>
              <div class="summary-text">task${upcomingTasks.length + overdueTasks.length !== 1 ? 's' : ''} need your attention today</div>
            </div>
  `;

  // Overdue tasks section
  if (overdueTasks.length > 0) {
    html += `
      <div class="section">
        <h2>🚨 Overdue Tasks</h2>
    `;

    overdueTasks.forEach(task => {
      const priorityClass = task.priority === 2 ? 'priority-high' : task.priority === 0 ? 'priority-low' : 'priority-normal';
      html += `
        <div class="task-item overdue ${priorityClass}">
          <div class="task-title">
            <a href="${appUrl}/tasks/${task.id}" target="_blank">${task.title}</a>
          </div>
          <div class="task-meta">
            ${formatPriority(task.priority || 1)} • Due: ${task.dueDate ? formatDate(new Date(task.dueDate)) : 'No date'}
            ${task.notes ? `<br><em>${task.notes.substring(0, 100)}${task.notes.length > 100 ? '...' : ''}</em>` : ''}
          </div>
          <div class="task-actions">
            <a href="${appUrl}/tasks/${task.id}" class="btn btn-primary" target="_blank">View Task</a>
            <a href="${appUrl}/api/tasks/${task.id}/complete?token=${generateTaskToken(task.id, user.id)}" class="btn btn-secondary" target="_blank">Mark Complete</a>
          </div>
        </div>
      `;
    });

    html += `</div>`;
  }

  // Upcoming tasks section
  if (upcomingTasks.length > 0) {
    html += `
      <div class="section">
        <h2>📅 Upcoming Tasks</h2>
    `;

    upcomingTasks.forEach(task => {
      const priorityClass = task.priority === 2 ? 'priority-high' : task.priority === 0 ? 'priority-low' : 'priority-normal';
      html += `
        <div class="task-item upcoming ${priorityClass}">
          <div class="task-title">
            <a href="${appUrl}/tasks/${task.id}" target="_blank">${task.title}</a>
          </div>
          <div class="task-meta">
            ${formatPriority(task.priority || 1)} • Due: ${task.dueDate ? formatDate(new Date(task.dueDate)) : 'No date'}
            ${task.notes ? `<br><em>${task.notes.substring(0, 100)}${task.notes.length > 100 ? '...' : ''}</em>` : ''}
          </div>
          <div class="task-actions">
            <a href="${appUrl}/tasks/${task.id}" class="btn btn-primary" target="_blank">View Task</a>
            <a href="${appUrl}/api/tasks/${task.id}/complete?token=${generateTaskToken(task.id, user.id)}" class="btn btn-secondary" target="_blank">Mark Complete</a>
          </div>
        </div>
      `;
    });

    html += `</div>`;
  }

  // No tasks message
  if (upcomingTasks.length === 0 && overdueTasks.length === 0) {
    html += `
      <div class="no-tasks">
        🎉 No urgent tasks today! You're all caught up.
      </div>
    `;
  }

  html += `
          </div>
          <div class="footer">
            <p style="margin-bottom: 16px; font-size: 16px; font-weight: 600; color: #1e293b;">
              <strong>Routine Mail</strong> - Your daily task companion
            </p>
            <p style="margin-bottom: 20px;">
              <a href="${appUrl}">🏠 Open App</a> •
              <a href="${appUrl}/settings">⚙️ Settings</a> •
              <a href="${appUrl}/tasks/new">➕ Add Task</a>
            </p>
            <p style="font-size: 12px; color: #94a3b8; margin: 0; line-height: 1.5;">
              You're receiving this because you have email reminders enabled.<br>
              <a href="${appUrl}/settings" style="color: #94a3b8;">Manage your email preferences</a>
            </p>
          </div>
        </div>
      </div>
    </body>
    </html>
  `;

  return html;
}

/**
 * Send daily reminder emails to all users
 */
export async function sendDailyReminders(): Promise<void> {
  console.log('Starting daily reminder email process...');

  const users = await getUsersNeedingReminders();
  console.log(`Found ${users.length} users to process`);

  for (const user of users) {
    try {
      const reminderData = await getTasksForEmailReminder(user.id, user.timezone || 'Asia/Kuala_Lumpur');

      if (!reminderData) continue;

      const { upcomingTasks, overdueTasks } = reminderData;
      const totalTasks = upcomingTasks.length + overdueTasks.length;

      // Only send email if there are tasks to remind about
      if (totalTasks > 0) {
        const emailHTML = generateReminderEmailHTML(reminderData);

        // Send actual email
        const { sendEmail } = await import('../email.js');
        const emailSent = await sendEmail(
          user.email,
          `Daily Task Reminder - ${totalTasks} task${totalTasks > 1 ? 's' : ''} need your attention`,
          emailHTML
        );

        // Log the email attempt
        await db.insert(emailLogs).values({
          userId: user.id,
          emailType: 'daily_reminder',
          taskCount: totalTasks,
          success: emailSent
        });

        if (emailSent) {
          console.log(`✅ Email sent successfully to ${user.email} with ${totalTasks} tasks`);
        } else {
          console.error(`❌ Failed to send email to ${user.email}`);
        }
      }
    } catch (error) {
      console.error(`Failed to send reminder to ${user.email}:`, error);

      // Log the failed attempt
      await db.insert(emailLogs).values({
        userId: user.id,
        emailType: 'daily_reminder',
        taskCount: 0,
        success: false
      });
    }
  }

  console.log('Daily reminder email process completed');
}

/**
 * Process specific reminder notifications
 */
export async function processReminderNotifications(): Promise<void> {
  const reminders = await getTasksNeedingReminders();

  for (const reminder of reminders) {
    try {
      // Get the task and user details
      const [task] = await db.select().from(tasks).where(eq(tasks.id, reminder.taskId));
      const [user] = await db.select().from(users).where(eq(users.id, reminder.userId));

      if (task && user) {
        // TODO: Send specific reminder email
        console.log(`Sending reminder for task "${task.title}" to ${user.email}`);

        // Mark reminder as sent
        await markReminderSent(reminder.id);
      }
    } catch (error) {
      console.error(`Failed to process reminder ${reminder.id}:`, error);
    }
  }
}
