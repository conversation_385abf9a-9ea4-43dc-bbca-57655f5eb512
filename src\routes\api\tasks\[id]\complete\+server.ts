import { json, redirect } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { verifyJWT } from '$lib/server/auth.js';
import { getTaskById, completeTask } from '$lib/server/db/operations.js';

export const POST: RequestHandler = async ({ params, cookies }) => {
  try {
    const token = cookies.get('auth-token');
    if (!token) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    const payload = verifyJWT(token);
    if (!payload) {
      return json({ error: 'Invalid token' }, { status: 401 });
    }

    // Check if task exists
    const existingTask = await getTaskById(params.id, payload.userId);
    if (!existingTask) {
      return json({ error: 'Task not found' }, { status: 404 });
    }

    if (existingTask.completed) {
      return json({ error: 'Task is already completed' }, { status: 400 });
    }

    await completeTask(params.id, payload.userId);

    // Return updated task
    const updatedTask = await getTaskById(params.id, payload.userId);
    return json({ task: updatedTask, message: 'Task completed successfully' });
  } catch (error) {
    console.error('Complete task error:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
};

// Handle GET requests from email links with token
export const GET: RequestHandler = async ({ params, url }) => {
  try {
    const token = url.searchParams.get('token');
    if (!token) {
      return redirect(302, `/tasks/${params.id}?error=missing-token`);
    }

    // Decode the token (simple base64url decode)
    try {
      const decoded = Buffer.from(token, 'base64url').toString();
      const [taskId, userId, timestamp] = decoded.split(':');

      // Verify token is for this task
      if (taskId !== params.id) {
        return redirect(302, `/tasks/${params.id}?error=invalid-token`);
      }

      // Check if token is not too old (24 hours)
      const tokenAge = Date.now() - parseInt(timestamp);
      if (tokenAge > 24 * 60 * 60 * 1000) {
        return redirect(302, `/tasks/${params.id}?error=token-expired`);
      }

      // Check if task exists and belongs to user
      const existingTask = await getTaskById(params.id, userId);
      if (!existingTask) {
        return redirect(302, `/tasks?error=task-not-found`);
      }

      if (existingTask.completed) {
        return redirect(302, `/tasks/${params.id}?message=already-completed`);
      }

      // Complete the task
      await completeTask(params.id, userId);

      // Redirect to task page with success message
      return redirect(302, `/tasks/${params.id}?message=completed-successfully`);

    } catch (decodeError) {
      console.error('Token decode error:', decodeError);
      return redirect(302, `/tasks/${params.id}?error=invalid-token`);
    }

  } catch (error) {
    console.error('Complete task via email error:', error);
    return redirect(302, `/tasks?error=server-error`);
  }
};
