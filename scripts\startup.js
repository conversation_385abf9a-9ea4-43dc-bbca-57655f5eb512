#!/usr/bin/env node

/**
 * Production startup script
 * This script runs before the server starts to initialize admin and scheduler
 */

import { config } from 'dotenv';

// Load environment variables
config({ path: '.env' });
config({ path: '.env.production' });

console.log('🚀 Running production startup initialization...');

async function runStartup() {
  try {
    // Import and run the startup initialization
    const { startupInitialization } = await import('../src/lib/server/startup.ts');

    // Wait for initialization to complete
    await startupInitialization();

    console.log('✅ Production startup initialization completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('❌ Production startup initialization failed:', error);
    process.exit(1);
  }
}

runStartup();
