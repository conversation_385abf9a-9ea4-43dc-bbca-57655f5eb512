import { json } from '@sveltejs/kit';
import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './$types';
import {
  getUserByEmail,
  createOtpRequest,
  getEmailLimitByEmail,
  createEmailLimit,
  updateEmailLimit,
  generateRequestId
} from '$lib/server/db/operations.js';
import { sendEmail } from '$lib/server/email.js';
import { generateOTPCode } from '$lib/server/auth.js';

export const POST: RequestHandler = async ({ request }) => {
  try {
    const { email } = await request.json();

    if (!email) {
      return json({ error: 'Email is required' }, { status: 400 });
    }

    // Check if user exists
    const user = await getUserByEmail(email);
    if (!user) {
      return json({ error: 'No account found with this email address' }, { status: 404 });
    }

    // Check email limits
    const emailLimit = await getEmailLimitByEmail(email);
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    if (emailLimit) {
      // Check daily limit (5 emails per day)
      if (emailLimit.dailyCount >= 5 &&
        emailLimit.lastEmailDate &&
        emailLimit.lastEmailDate >= today) {
        return json({
          error: 'Daily email limit reached. Please try again tomorrow.'
        }, { status: 429 });
      }

      // Check cooldown (3 minutes between emails)
      if (emailLimit.lastEmailDate &&
        (now.getTime() - emailLimit.lastEmailDate.getTime()) < 180000) {
        const cooldownRemaining = Math.ceil((180000 - (now.getTime() - emailLimit.lastEmailDate.getTime())) / 1000);
        return json({
          error: 'Please wait before requesting another code',
          cooldownRemaining
        }, { status: 429 });
      }

      // Update existing limit
      const newDailyCount = emailLimit.lastEmailDate && emailLimit.lastEmailDate >= today
        ? emailLimit.dailyCount + 1
        : 1;

      await updateEmailLimit(email, newDailyCount, now);
    } else {
      // Create new email limit record
      await createEmailLimit(email, 1, now);
    }

    // Generate OTP and create request
    const otpCode = generateOTPCode();
    const requestId = generateRequestId();
    const expiresAt = new Date(Date.now() + 5 * 60 * 1000); // 5 minutes
    const otpRequest = await createOtpRequest({
      requestId,
      email,
      userId: user.id,
      code: otpCode,
      purpose: 'password_reset',
      expiresAt
    });

    // Send email
    const emailHtml = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #4299e1, #63b3ed); padding: 2rem; text-align: center;">
          <h1 style="color: white; margin: 0; font-size: 1.75rem;">Routine Mail</h1>
        </div>
        
        <div style="padding: 2rem; background: #f8fafc;">
          <h2 style="color: #2d3748; margin-bottom: 1rem;">Password Reset Request</h2>
          
          <p style="color: #4a5568; line-height: 1.6; margin-bottom: 1.5rem;">
            We received a request to reset your password. Use the code below to reset your password:
          </p>
          
          <div style="background: white; border: 2px solid #4299e1; border-radius: 8px; padding: 1.5rem; text-align: center; margin: 2rem 0;">
            <div style="font-size: 2rem; font-weight: bold; color: #2d3748; letter-spacing: 0.5rem;">${otpCode}</div>
          </div>
          
          <p style="color: #4a5568; line-height: 1.6; margin-bottom: 1rem;">
            This code will expire in <strong>5 minutes</strong>.
          </p>
          
          <p style="color: #4a5568; line-height: 1.6; margin-bottom: 1rem;">
            If you didn't request this password reset, please ignore this email.
          </p>
          
          <div style="border-top: 1px solid #e2e8f0; padding-top: 1.5rem; margin-top: 2rem;">
            <p style="color: #718096; font-size: 0.875rem; margin: 0;">
              This is an automated message from Routine Mail. Please do not reply to this email.
            </p>
          </div>
        </div>
      </div>
    `;

    const emailSent = await sendEmail(
      email,
      'Password Reset Code - Routine Mail',
      emailHtml
    );

    if (!emailSent) {
      return json({ error: 'Failed to send password reset email' }, { status: 500 });
    }

    console.log(`[Forgot Password] OTP sent to ${email}, Request ID: ${otpRequest.requestId}`);

    return json({
      message: 'Password reset code sent successfully',
      requestId: otpRequest.requestId
    });

  } catch (error) {
    console.error('[Forgot Password] Error sending OTP:', error);
    return json({ error: 'Failed to send password reset code' }, { status: 500 });
  }
};
