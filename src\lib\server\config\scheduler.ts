import { config } from 'dotenv';

// Load environment variables
config({ path: '.env' });

/**
 * Scheduler configuration loaded from environment variables
 */
export const schedulerConfig = {
  // Task maintenance interval in hours (legacy, kept for backward compatibility)
  maintenanceIntervalHours: parseInt(process.env.SCHEDULER_MAINTENANCE_INTERVAL_HOURS || '6'),

  // Initial delay in seconds before running first maintenance after startup
  initialDelaySeconds: parseInt(process.env.SCHEDULER_INITIAL_DELAY_SECONDS || '5'),

  // Cron expressions for scheduled tasks
  // Default: every 6 hours at minute 0 (00:00, 06:00, 12:00, 18:00)
  maintenanceCronExpression: process.env.SCHEDULER_MAINTENANCE_CRON || '0 */6 * * *',

  // Email reminder cron expression
  // Default: daily at 6:00 AM
  emailReminderCronExpression: process.env.EMAIL_REMINDER_CRON || '0 6 * * *',

  // Email reminder configuration (legacy, kept for backward compatibility)
  emailReminderHour: parseInt(process.env.EMAIL_REMINDER_HOUR || '6'),
  emailReminderMinute: parseInt(process.env.EMAIL_REMINDER_MINUTE || '0'),

  // Default number of days before due date to send reminders
  defaultReminderDays: parseInt(process.env.DEFAULT_REMINDER_DAYS || '3'),

  // Recurring tasks configuration
  recurringGenerationYears: parseInt(process.env.RECURRING_GENERATION_YEARS || '10'),
  recurringMaintenanceMonths: parseInt(process.env.RECURRING_MAINTENANCE_MONTHS || '3'),

  // Cron job configuration
  cronSecret: process.env.CRON_SECRET || 'dev-secret-change-in-production',

  // Timezone for cron jobs
  cronTimezone: process.env.CRON_TIMEZONE || 'Asia/Kuala_Lumpur'
};

/**
 * Get scheduler configuration with validation
 */
export function getSchedulerConfig() {
  // Validate configuration values
  const config = { ...schedulerConfig };

  // Ensure positive values
  if (config.maintenanceIntervalHours <= 0) {
    console.warn('⚠️  Invalid SCHEDULER_MAINTENANCE_INTERVAL_HOURS, using default: 6');
    config.maintenanceIntervalHours = 6;
  }

  if (config.initialDelaySeconds < 0) {
    console.warn('⚠️  Invalid SCHEDULER_INITIAL_DELAY_SECONDS, using default: 5');
    config.initialDelaySeconds = 5;
  }

  if (config.emailReminderHour < 0 || config.emailReminderHour > 23) {
    console.warn('⚠️  Invalid EMAIL_REMINDER_HOUR (must be 0-23), using default: 6');
    config.emailReminderHour = 6;
  }

  if (config.emailReminderMinute < 0 || config.emailReminderMinute > 59) {
    console.warn('⚠️  Invalid EMAIL_REMINDER_MINUTE (must be 0-59), using default: 0');
    config.emailReminderMinute = 0;
  }

  if (config.defaultReminderDays <= 0) {
    console.warn('⚠️  Invalid DEFAULT_REMINDER_DAYS, using default: 3');
    config.defaultReminderDays = 3;
  }

  if (config.recurringGenerationYears <= 0) {
    console.warn('⚠️  Invalid RECURRING_GENERATION_YEARS, using default: 10');
    config.recurringGenerationYears = 10;
  }

  if (config.recurringMaintenanceMonths <= 0) {
    console.warn('⚠️  Invalid RECURRING_MAINTENANCE_MONTHS, using default: 3');
    config.recurringMaintenanceMonths = 3;
  }

  return config;
}

/**
 * Log current scheduler configuration
 */
export function logSchedulerConfig() {
  const config = getSchedulerConfig();

  console.log('📋 Scheduler Configuration:');
  console.log(`  🔄 Maintenance cron: ${config.maintenanceCronExpression}`);
  console.log(`  📧 Email reminder cron: ${config.emailReminderCronExpression}`);
  console.log(`  🌍 Timezone: ${config.cronTimezone}`);
  console.log(`  ⏱️  Initial delay: ${config.initialDelaySeconds} seconds`);
  console.log(`  📅 Default reminder days: ${config.defaultReminderDays} days`);
  console.log(`  🔁 Recurring generation: ${config.recurringGenerationYears} years`);
  console.log(`  🔧 Recurring maintenance: ${config.recurringMaintenanceMonths} months`);
}

export default schedulerConfig;
