{"name": "routine-mail-sveltekit", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "npx drizzle-kit push && vite dev --host 0.0.0.0", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "db:generate": "npx drizzle-kit generate", "db:migrate": "npx drizzle-kit migrate", "db:push": "npx drizzle-kit push", "db:studio": "drizzle-kit studio", "prod": "npx cross-env NODE_ENV=production npm run db:push && npm run build && npm run start", "start": "npx cross-env NODE_ENV=production npm run db:push && npx tsx scripts/startup.js && npx cross-env NODE_ENV=production node build"}, "devDependencies": {"@sveltejs/adapter-auto": "^6.0.0", "@sveltejs/kit": "^2.22.0", "@sveltejs/vite-plugin-svelte": "^6.0.0", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "autoprefixer": "^10.4.21", "cross-env": "^10.0.0", "postcss": "^8.5.6", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "tailwindcss": "^3.4.0", "tsx": "^4.20.3", "typescript": "^5.0.0", "vite": "^7.0.4"}, "dependencies": {"@fullcalendar/core": "^6.1.18", "@fullcalendar/daygrid": "^6.1.18", "@fullcalendar/interaction": "^6.1.18", "@fullcalendar/list": "^6.1.18", "@fullcalendar/timegrid": "^6.1.18", "@sveltejs/adapter-node": "^5.3.1", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.17", "@types/pg": "^8.15.4", "@types/uuid": "^10.0.0", "bcryptjs": "^3.0.2", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "dotenv": "^17.2.0", "drizzle-kit": "^0.31.4", "drizzle-orm": "^0.44.3", "flatpickr": "^4.6.13", "jsonwebtoken": "^9.0.2", "node-cron": "^4.2.1", "nodemailer": "^7.0.5", "pg": "^8.16.3", "postgres": "^3.4.7", "uuid": "^11.1.0"}}