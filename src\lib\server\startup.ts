import { config } from 'dotenv';
import { taskScheduler } from './scheduler.js';

// Load environment variables
config({ path: '.env' });

console.log('🚀 Server startup initialization...');

// Initialize admin on server startup
async function initializeAdmin() {
  try {
    console.log('🔧 Starting admin initialization...');

    // Import admin initialization functions
    const { getAdminUser, getUserByEmail, createUserByAdmin, updateUserAdminStatus } = await import('./db/operations.js');
    const { generateRandomPassword, hashPassword } = await import('./auth.js');
    const { sendEmail } = await import('./email.js');

    const adminEmail = process.env.ADMIN_EMAIL;
    console.log('ADMIN_EMAIL from env:', adminEmail);

    if (!adminEmail) {
      console.log('ADMIN_EMAIL not configured, skipping admin initialization');
      return;
    }

    // Check if admin already exists
    const existingAdmin = await getAdminUser();
    if (existingAdmin) {
      console.log(`✅ Admin already exists: ${existingAdmin.email}`);
      return;
    }

    // Check if user with admin email exists
    let adminUser = await getUserByEmail(adminEmail);

    if (adminUser) {
      // User exists, just make them admin
      await updateUserAdminStatus(adminUser.id, true);
      console.log(`✅ Existing user promoted to admin: ${adminUser.email}`);
    } else {
      // Create new admin user
      const password = generateRandomPassword();
      const hashedPassword = await hashPassword(password);

      adminUser = await createUserByAdmin({
        email: adminEmail,
        name: 'Administrator',
        password: hashedPassword,
        isAdmin: true
      });

      console.log(`✅ Admin user created: ${adminUser.email}`);

      // Send admin credentials via email
      try {
        const emailHtml = `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #333;">Routine Mail - Admin Account Created</h2>
            <p>Your admin account has been created successfully.</p>
            <div style="background-color: #f5f5f5; padding: 20px; border-radius: 5px; margin: 20px 0;">
              <h3 style="margin-top: 0;">Login Credentials</h3>
              <p><strong>Email:</strong> ${adminEmail}</p>
              <p><strong>Password:</strong> ${password}</p>
            </div>
            <p style="color: #666; font-size: 14px;">
              Please log in and change your password immediately for security.
            </p>
            <p style="color: #666; font-size: 14px;">
              Login URL: ${process.env.APP_URL || 'http://localhost:3000'}/login
            </p>
          </div>
        `;

        const emailSent = await sendEmail(
          adminEmail,
          'Routine Mail - Admin Account Created',
          emailHtml
        );

        if (emailSent) {
          console.log(`✅ Admin credentials sent to ${adminEmail}`);
        } else {
          console.error(`❌ Failed to send admin credentials to ${adminEmail}`);
        }
      } catch (emailError) {
        console.error('❌ Failed to send admin credentials email:', emailError);
      }
    }
  } catch (error) {
    console.error('❌ Admin initialization error:', error);
  }
}

// Initialize task scheduler
function initializeScheduler() {
  console.log('📅 Starting task scheduler...');
  
  if (!taskScheduler.getStatus().isRunning) {
    taskScheduler.start();
    console.log('✅ Task scheduler started successfully');
  } else {
    console.log('✅ Task scheduler already running');
  }
}

// Main startup function
export async function startupInitialization() {
  console.log('🚀 Running startup initialization...');
  
  try {
    // Initialize admin first
    await initializeAdmin();
    
    // Then initialize scheduler
    initializeScheduler();
    
    console.log('✅ Startup initialization completed successfully');
  } catch (error) {
    console.error('❌ Startup initialization failed:', error);
  }
}

// Auto-run initialization only when imported by vite.config.ts in development
// In production, this will be called explicitly by scripts/startup.js
// Don't auto-run to avoid duplicate execution
