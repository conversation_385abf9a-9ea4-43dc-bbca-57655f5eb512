import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { requireAdmin } from '$lib/server/admin.js';
import { getUserById } from '$lib/server/db/operations.js';
import { sendEmail } from '$lib/server/email.js';

export const POST: RequestHandler = async (event) => {
  try {
    await requireAdmin(event);

    const { id } = event.params;

    if (!id) {
      return json({ error: 'User ID is required' }, { status: 400 });
    }

    // Check if user exists
    const user = await getUserById(id);
    if (!user) {
      return json({ error: 'User not found' }, { status: 404 });
    }

    // Create test email content
    const emailHtml = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #4299e1, #63b3ed); padding: 2rem; text-align: center;">
          <h1 style="color: white; margin: 0; font-size: 1.75rem;">Routine Mail</h1>
        </div>

        <div style="padding: 2rem; background: white;">
          <h2 style="color: #2d3748; margin-bottom: 1rem;">Test Email</h2>

          <p style="color: #4a5568; line-height: 1.6; margin-bottom: 1rem;">
            Hello ${user.name || user.email},
          </p>

          <p style="color: #4a5568; line-height: 1.6; margin-bottom: 1rem;">
            This is a test email sent by the administrator to verify that email delivery is working correctly for your account.
          </p>

          <div style="background: #f0f9ff; border: 2px solid #bae6fd; border-radius: 8px; padding: 1.5rem; margin: 1.5rem 0;">
            <p style="margin: 0; color: #0c4a6e;">
              <strong>✓ Email delivery test successful!</strong>
            </p>
            <p style="margin: 0.5rem 0 0 0; color: #0369a1; font-size: 0.875rem;">
              Sent at: ${new Date().toLocaleString()}
            </p>
          </div>

          <p style="color: #4a5568; line-height: 1.6; margin-bottom: 1rem;">
            If you received this email, it means your email notifications are working properly. You should receive task reminders and other important notifications from Routine Mail.
          </p>

          <div style="text-align: center; margin: 2rem 0;">
            <a href="${process.env.APP_URL || 'http://localhost:3001'}/dashboard"
               style="background: #4299e1; color: white; padding: 0.75rem 1.5rem; text-decoration: none; border-radius: 6px; display: inline-block;">
              Go to Dashboard
            </a>
          </div>

          <div style="border-top: 1px solid #e2e8f0; padding-top: 1.5rem; margin-top: 2rem;">
            <p style="color: #718096; font-size: 0.875rem; margin: 0;">
              This is a test message from Routine Mail. If you have any questions, please contact your administrator.
            </p>
          </div>
        </div>
      </div>
    `;

    // Send test email
    const emailSent = await sendEmail(
      user.email,
      'Routine Mail - Test Email',
      emailHtml
    );

    if (!emailSent) {
      return json({ error: 'Failed to send test email' }, { status: 500 });
    }

    return json({ 
      message: 'Test email sent successfully',
      recipient: user.email,
      sentAt: new Date().toISOString()
    });
  } catch (error) {
    console.error('Admin test email error:', error);
    return json({ error: 'Unauthorized' }, { status: 401 });
  }
};
