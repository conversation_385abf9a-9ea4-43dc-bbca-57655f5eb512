import { relations } from "drizzle-orm/relations";
import { users, sessions, otpRequests, officeFlowLinks, categories, emailLogs, tasks, taskReminders } from "./schema";

export const sessionsRelations = relations(sessions, ({one}) => ({
	user: one(users, {
		fields: [sessions.userId],
		references: [users.id]
	}),
}));

export const usersRelations = relations(users, ({many}) => ({
	sessions: many(sessions),
	otpRequests: many(otpRequests),
	officeFlowLinks: many(officeFlowLinks),
	categories: many(categories),
	emailLogs: many(emailLogs),
	tasks: many(tasks),
	taskReminders: many(taskReminders),
}));

export const otpRequestsRelations = relations(otpRequests, ({one}) => ({
	user: one(users, {
		fields: [otpRequests.userId],
		references: [users.id]
	}),
}));

export const officeFlowLinksRelations = relations(officeFlowLinks, ({one}) => ({
	user: one(users, {
		fields: [officeFlowLinks.userId],
		references: [users.id]
	}),
}));

export const categoriesRelations = relations(categories, ({one, many}) => ({
	user: one(users, {
		fields: [categories.userId],
		references: [users.id]
	}),
	tasks: many(tasks),
}));

export const emailLogsRelations = relations(emailLogs, ({one}) => ({
	user: one(users, {
		fields: [emailLogs.userId],
		references: [users.id]
	}),
}));

export const tasksRelations = relations(tasks, ({one, many}) => ({
	user: one(users, {
		fields: [tasks.userId],
		references: [users.id]
	}),
	category: one(categories, {
		fields: [tasks.categoryId],
		references: [categories.id]
	}),
	taskReminders: many(taskReminders),
}));

export const taskRemindersRelations = relations(taskReminders, ({one}) => ({
	user: one(users, {
		fields: [taskReminders.userId],
		references: [users.id]
	}),
	task: one(tasks, {
		fields: [taskReminders.taskId],
		references: [tasks.id]
	}),
}));