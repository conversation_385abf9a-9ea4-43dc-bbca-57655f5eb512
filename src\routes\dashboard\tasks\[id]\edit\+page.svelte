<script lang="ts">
  import { goto } from '$app/navigation';
  import type { PageData } from './$types';
  import TaskForm from '$lib/components/TaskForm.svelte';
  import PageTransition from '$lib/components/ui/PageTransition.svelte';
  import { toasts } from '$lib/stores/toast';

  export let data: PageData;

  const { task, categories } = data;

  // Initialize form fields with existing task data
  let title = task.title;
  let notes = task.notes || '';
  let priority = task.priority;
  let categoryId = task.categoryId || '';
  let dueDate = '';
  let dueTime = '';
  let hasDueDate = !!task.dueDate;
  // Normalize subtasks to string[] for form inputs
  let subtasks: string[] = Array.isArray(task.subtasks)
    ? task.subtasks.map((s: any) => (typeof s === 'string' ? s : (s?.text ?? '')))
    : [''];
  let loading = false;
  let error = '';
  let success = '';
  let showCategoryDropdown = false;

  // New recurrence rule state, initialized with task data
  let recurrenceRule: any = task.recurrenceRule || null;
  let hasRecurrence = !!task.recurrenceRule;
  let reminderDays = task.reminderDays ?? 3;

  // Initialize hasReminder and reminderType based on existing task data
  let hasReminder = !!task.dueDate || !!task.recurrenceRule;
  let reminderType: 'once' | 'recurring' = task.recurrenceRule ? 'recurring' : 'once';

  // Initialize date and time from existing task
  import { fromUTCToParts } from '$lib/utils/datetime';
  if (task.dueDate) {
    const parts = fromUTCToParts(task.dueDate, data.user?.timezone || 'Asia/Kuala_Lumpur');
    if (reminderType === 'once') {
      // For one-time reminders, use both date and time
      dueDate = parts.date;
      dueTime = parts.time;
    } else if (reminderType === 'recurring') {
      // For recurring tasks, only use the time part
      dueTime = parts.time;
    }
  } else if (reminderType === 'recurring') {
    // For recurring tasks without dueDate, set default time
    dueTime = '08:00';
  }

  // Ensure subtasks array has at least one empty string for the UI
  if (subtasks.length === 0) {
    subtasks = [''];
  }

  async function handleSubmit(event: CustomEvent) {
    const formData = event.detail;
    
    loading = true;
    error = '';

    try {
      const response = await fetch(`/api/tasks/${task.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (response.ok) {
        // Show success toast and redirect
        toasts.success('Task updated!', `"${formData.title}" has been successfully updated.`);
        goto(`/dashboard/tasks/${task.id}`, { invalidateAll: true });
      } else {
        error = result.error || 'Failed to update task';
        toasts.error('Failed to update task', error);
      }
    } catch (err) {
      error = 'Network error. Please try again.';
      toasts.error('Network error', 'Unable to update task. Please check your connection.');
    } finally {
      loading = false;
    }
  }


</script>

<svelte:head>
  <title>Edit {task.title} - Routine Mail</title>
</svelte:head>

<PageTransition>
<div class="page-container">
  <!-- Desktop Header -->
  <div class="page-header hidden md:block">
    <h1 class="page-title">Edit Task</h1>
    <p class="page-subtitle">Update your task details</p>
  </div>

  <TaskForm
    mode="edit"
    bind:title
    bind:notes
    bind:priority
    bind:categoryId
    bind:dueDate
    bind:dueTime
    bind:hasDueDate
    bind:subtasks
    bind:loading
    bind:error
    bind:success
    bind:showCategoryDropdown
    bind:hasRecurrence
    bind:recurrenceRule
    bind:reminderDays
    bind:hasReminder
    bind:reminderType
    userTimezone={data.user?.timezone || 'Asia/Kuala_Lumpur'}
    categories={categories}
    on:submit={handleSubmit}
  />
</div>

<style>
  .page-container {
    max-width: 800px;
    margin: 0 auto;
  }

  .page-header {
    margin-bottom: 1rem;
  }

  .page-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: #1a202c;
    margin-bottom: 0.25rem;
    letter-spacing: -0.02em;
  }

  .page-subtitle {
    color: #6b7280;
    font-size: 0.875rem;
    margin: 0;
    font-weight: 500;
  }

  /* Mobile-first improvements */
  @media (max-width: 768px) {
    .page-header {
      margin-bottom: 0.75rem;
    }
  }
</style>

</PageTransition>
