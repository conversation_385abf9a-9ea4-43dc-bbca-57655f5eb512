import { pgTable, uuid, text, integer, timestamp, foreignKey, unique, boolean, index, jsonb } from "drizzle-orm/pg-core"
import { sql } from "drizzle-orm"



export const emailLimits = pgTable("email_limits", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	email: text().notNull(),
	requestCount: integer("request_count").default(0),
	lastRequestDate: timestamp("last_request_date", { mode: 'string' }).defaultNow(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
});

export const sessions = pgTable("sessions", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	userId: uuid("user_id").notNull(),
	token: text().notNull(),
	expiresAt: timestamp("expires_at", { mode: 'string' }).notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
}, (table) => [
	foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "sessions_user_id_users_id_fk"
		}).onDelete("cascade"),
	unique("sessions_token_unique").on(table.token),
]);

export const otpRequests = pgTable("otp_requests", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	requestId: uuid("request_id").notNull(),
	email: text().notNull(),
	userId: uuid("user_id"),
	code: text().notNull(),
	attempts: integer().default(0),
	isUsed: boolean("is_used").default(false),
	expiresAt: timestamp("expires_at", { mode: 'string' }).notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
	purpose: text().default('registration'),
}, (table) => [
	foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "otp_requests_user_id_users_id_fk"
		}).onDelete("cascade"),
	unique("otp_requests_request_id_unique").on(table.requestId),
]);

export const officeFlowLinks = pgTable("office_flow_links", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	userId: uuid("user_id").notNull(),
	officeFlowUserId: text("office_flow_user_id").notNull(),
	officeFlowEmail: text("office_flow_email").notNull(),
	officeFlowName: text("office_flow_name"),
	officeFlowAvatar: text("office_flow_avatar"),
	officeFlowDepartment: text("office_flow_department"),
	officeFlowPosition: text("office_flow_position"),
	accessToken: text("access_token"),
	refreshToken: text("refresh_token"),
	tokenExpiresAt: timestamp("token_expires_at", { mode: 'string' }),
	isActive: boolean("is_active").default(true),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow(),
}, (table) => [
	foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "office_flow_links_user_id_users_id_fk"
		}).onDelete("cascade"),
]);

export const users = pgTable("users", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	email: text().notNull(),
	password: text().notNull(),
	name: text(),
	isVerified: boolean("is_verified").default(false),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow(),
	timezone: text().default('Asia/Kuala_Lumpur'),
	emailFrequencyDays: integer("email_frequency_days").default(1),
	emailPreviewDays: integer("email_preview_days").default(1),
	dailyEmailCount: integer("daily_email_count").default(0),
	lastEmailDate: timestamp("last_email_date", { mode: 'string' }),
	emailRemindersEnabled: boolean("email_reminders_enabled").default(true),
	isAdmin: boolean("is_admin").default(false),
	isSuspended: boolean("is_suspended").default(false),
}, (table) => [
	index("idx_users_email").using("btree", table.email.asc().nullsLast().op("text_ops")),
	index("idx_users_timezone").using("btree", table.timezone.asc().nullsLast().op("text_ops")),
	unique("users_email_unique").on(table.email),
]);

export const categories = pgTable("categories", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	userId: uuid("user_id").notNull(),
	name: text().notNull(),
	color: text().default('#6B7280'),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
}, (table) => [
	index("idx_categories_user_id").using("btree", table.userId.asc().nullsLast().op("uuid_ops")),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "categories_user_id_users_id_fk"
		}).onDelete("cascade"),
]);

export const emailLogs = pgTable("email_logs", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	userId: uuid("user_id").notNull(),
	emailType: text("email_type").notNull(),
	sentAt: timestamp("sent_at", { mode: 'string' }).defaultNow(),
	taskCount: integer("task_count").default(0),
	success: boolean().default(true),
}, (table) => [
	index("idx_email_logs_sent_at").using("btree", table.sentAt.asc().nullsLast().op("timestamp_ops")),
	index("idx_email_logs_user_sent").using("btree", table.userId.asc().nullsLast().op("timestamp_ops"), table.sentAt.asc().nullsLast().op("timestamp_ops")),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "email_logs_user_id_users_id_fk"
		}).onDelete("cascade"),
]);

export const tasks = pgTable("tasks", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	userId: uuid("user_id").notNull(),
	title: text().notNull(),
	notes: text(),
	priority: integer().default(1),
	categoryId: uuid("category_id"),
	dueDate: timestamp("due_date", { mode: 'string' }),
	completed: boolean().default(false),
	completedAt: timestamp("completed_at", { mode: 'string' }),
	recurrenceRule: jsonb("recurrence_rule"),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow(),
	subtasks: jsonb(),
	reminderDays: integer("reminder_days"),
	recurringGroupId: uuid("recurring_group_id"),
	isRecurringTemplate: boolean("is_recurring_template").default(false),
	recurringInstanceDate: timestamp("recurring_instance_date", { mode: 'string' }),
}, (table) => [
	index("idx_tasks_completed").using("btree", table.completed.asc().nullsLast().op("bool_ops")),
	index("idx_tasks_due_date").using("btree", table.dueDate.asc().nullsLast().op("timestamp_ops")),
	index("idx_tasks_instance_date").using("btree", table.recurringInstanceDate.asc().nullsLast().op("timestamp_ops")),
	index("idx_tasks_recurring_group").using("btree", table.recurringGroupId.asc().nullsLast().op("uuid_ops")),
	index("idx_tasks_template").using("btree", table.isRecurringTemplate.asc().nullsLast().op("bool_ops")),
	index("idx_tasks_user_completed").using("btree", table.userId.asc().nullsLast().op("uuid_ops"), table.completed.asc().nullsLast().op("uuid_ops")),
	index("idx_tasks_user_due").using("btree", table.userId.asc().nullsLast().op("timestamp_ops"), table.dueDate.asc().nullsLast().op("uuid_ops")),
	index("idx_tasks_user_id").using("btree", table.userId.asc().nullsLast().op("uuid_ops")),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "tasks_user_id_users_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.categoryId],
			foreignColumns: [categories.id],
			name: "tasks_category_id_categories_id_fk"
		}).onDelete("set null"),
]);

export const taskReminders = pgTable("task_reminders", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	userId: uuid("user_id").notNull(),
	taskId: uuid("task_id").notNull(),
	reminderDate: timestamp("reminder_date", { mode: 'string' }).notNull(),
	reminderType: text("reminder_type").notNull(),
	sent: boolean().default(false),
	sentAt: timestamp("sent_at", { mode: 'string' }),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
}, (table) => [
	index("idx_reminders_date").using("btree", table.reminderDate.asc().nullsLast().op("timestamp_ops")),
	index("idx_reminders_sent").using("btree", table.sent.asc().nullsLast().op("bool_ops")),
	index("idx_reminders_task").using("btree", table.taskId.asc().nullsLast().op("uuid_ops")),
	index("idx_reminders_user_date").using("btree", table.userId.asc().nullsLast().op("uuid_ops"), table.reminderDate.asc().nullsLast().op("uuid_ops")),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "task_reminders_user_id_users_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.taskId],
			foreignColumns: [tasks.id],
			name: "task_reminders_task_id_tasks_id_fk"
		}).onDelete("cascade"),
]);
