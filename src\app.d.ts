// See https://svelte.dev/docs/kit/types#app.d.ts
// for information about these interfaces
declare global {
	namespace App {
		// interface Error {}
		interface Locals {
			user?: {
				id: string;
				email: string;
				name: string | null;
				isVerified: boolean;
				isAdmin: boolean;
				isSuspended: boolean;
				emailRemindersEnabled: boolean;
				timezone: string;
				emailFrequencyDays: number;
				emailPreviewDays: number;
			};
		}
		// interface PageData {}
		// interface PageState {}
		// interface Platform {}
	}
}

export { };
