import cron from 'node-cron';
import { maintainRecurringInstances } from './services/cronJobs.js';
import { sendDailyReminders, processReminderNotifications } from './services/emailReminders.js';
import { getSchedulerConfig, logSchedulerConfig } from './config/scheduler.js';

/**
 * Advanced scheduler using node-cron for recurring tasks maintenance and email reminders
 */
class TaskScheduler {
  private cronJobs: Map<string, cron.ScheduledTask> = new Map();
  private isRunning = false;

  /**
   * Start the scheduler
   */
  start(): void {
    if (this.isRunning) {
      console.log('Scheduler is already running');
      return;
    }

    console.log('Starting task scheduler with node-cron...');
    this.isRunning = true;

    // Get configuration from environment variables
    const config = getSchedulerConfig();

    // Log configuration
    logSchedulerConfig();

    try {
      // Schedule recurring tasks maintenance
      const maintenanceJob = cron.schedule(
        config.maintenanceCronExpression,
        async () => {
          try {
            console.log('Running scheduled recurring instances maintenance...');
            await maintainRecurringInstances();
          } catch (error) {
            console.error('Error in scheduled maintenance:', error);
          }
        },
        {
          scheduled: false,
          timezone: config.cronTimezone
        }
      );

      this.cronJobs.set('maintenance', maintenanceJob);

      // Schedule daily email reminders
      const emailReminderJob = cron.schedule(
        config.emailReminderCronExpression,
        async () => {
          try {
            console.log('Running scheduled email reminders...');
            await Promise.all([
              sendDailyReminders(),
              processReminderNotifications()
            ]);
          } catch (error) {
            console.error('Error in scheduled email reminders:', error);
          }
        },
        {
          scheduled: false,
          timezone: config.cronTimezone
        }
      );

      this.cronJobs.set('emailReminders', emailReminderJob);

      // Start all cron jobs
      this.cronJobs.forEach((job, name) => {
        job.start();
        console.log(`✅ Started cron job: ${name}`);
      });

      // Run initial maintenance on startup (after delay)
      setTimeout(async () => {
        try {
          console.log('Running initial recurring instances maintenance...');
          await maintainRecurringInstances();
        } catch (error) {
          console.error('Error in initial maintenance:', error);
        }
      }, config.initialDelaySeconds * 1000);

      console.log('Task scheduler started successfully with node-cron');
    } catch (error) {
      console.error('Failed to start task scheduler:', error);
      this.isRunning = false;
      throw error;
    }
  }

  /**
   * Stop the scheduler
   */
  stop(): void {
    if (!this.isRunning) {
      console.log('Scheduler is not running');
      return;
    }

    console.log('Stopping task scheduler...');

    // Stop and destroy all cron jobs
    this.cronJobs.forEach((job, name) => {
      job.stop();
      job.destroy();
      console.log(`🛑 Stopped cron job: ${name}`);
    });

    this.cronJobs.clear();
    this.isRunning = false;

    console.log('Task scheduler stopped');
  }

  /**
   * Get scheduler status
   */
  getStatus(): { isRunning: boolean; cronJobCount: number; cronJobs: string[] } {
    return {
      isRunning: this.isRunning,
      cronJobCount: this.cronJobs.size,
      cronJobs: Array.from(this.cronJobs.keys())
    };
  }

  /**
   * Manually trigger maintenance (for testing or manual runs)
   */
  async runMaintenance(): Promise<void> {
    console.log('Manually triggering recurring instances maintenance...');
    try {
      await maintainRecurringInstances();
      console.log('Manual maintenance completed successfully');
    } catch (error) {
      console.error('Error in manual maintenance:', error);
      throw error;
    }
  }

  /**
   * Manually trigger email reminders (for testing or manual runs)
   */
  async runEmailReminders(): Promise<void> {
    console.log('Manually triggering email reminders...');
    try {
      await Promise.all([
        sendDailyReminders(),
        processReminderNotifications()
      ]);
      console.log('Manual email reminders completed successfully');
    } catch (error) {
      console.error('Error in manual email reminders:', error);
      throw error;
    }
  }

  /**
   * Get cron job details
   */
  getCronJobDetails(): Array<{ name: string; running: boolean; expression?: string }> {
    const config = getSchedulerConfig();
    const details: Array<{ name: string; running: boolean; expression?: string }> = [];

    this.cronJobs.forEach((job, name) => {
      let expression: string | undefined;

      switch (name) {
        case 'maintenance':
          expression = config.maintenanceCronExpression;
          break;
        case 'emailReminders':
          expression = config.emailReminderCronExpression;
          break;
      }

      details.push({
        name,
        running: job.getStatus() === 'scheduled',
        expression
      });
    });

    return details;
  }
}

// Export singleton instance
export const taskScheduler = new TaskScheduler();

// Note: Auto-start is now handled by startup.ts module
