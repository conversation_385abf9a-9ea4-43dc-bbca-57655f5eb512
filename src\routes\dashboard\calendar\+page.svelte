<script lang="ts">
  import { onMount, onDestroy } from 'svelte';
  import { goto } from '$app/navigation';
  import { Calendar } from '@fullcalendar/core';
  import dayGridPlugin from '@fullcalendar/daygrid';
  import timeGridPlugin from '@fullcalendar/timegrid';
  import listPlugin from '@fullcalendar/list';
  import interactionPlugin from '@fullcalendar/interaction';
  import type { PageData } from './$types';
  import PageTransition from '$lib/components/ui/PageTransition.svelte';
  import Button from '$lib/components/ui/Button.svelte';
  import { formatUnifiedDate, formatShortDate, fromUTCToParts } from '$lib/utils/datetime';

  export let data: PageData;

  let calendarEl: HTMLElement;
  let calendar: Calendar;
  let currentView = 'dayGridMonth';

  // Convert tasks to FullCalendar events format with proper timezone handling
  $: events = data.tasks
    .filter(task => task.dueDate) // Only include tasks with due dates
    .map(task => {
      // 正确转换 UTC 到用户时区，然后只使用日期部分
      const userTz = data.user?.timezone || 'Asia/Kuala_Lumpur';
      const { date: localDateStr } = fromUTCToParts(task.dueDate, userTz);

      // Find category color or use default based on priority
      const category = data.categories?.find(cat => cat.id === task.categoryId);
      const categoryColor = category?.color;

      // Use category color if available, otherwise fall back to priority colors
      let backgroundColor, borderColor;
      if (task.completed) {
        backgroundColor = '#94a3b8';
        borderColor = '#64748b';
      } else if (categoryColor) {
        backgroundColor = categoryColor;
        borderColor = categoryColor;
      } else {
        // Fallback to priority colors if no category
        backgroundColor = task.priority === 3 ? '#dc2626' :
                         task.priority === 2 ? '#ea580c' : '#059669';
        borderColor = task.priority === 3 ? '#b91c1c' :
                     task.priority === 2 ? '#c2410c' : '#047857';
      }

      return {
        id: task.id,
        title: task.title,
        start: localDateStr, // 使用转换后的本地日期
        allDay: true,
        backgroundColor,
        borderColor,
        textColor: task.completed ? '#374151' : '#ffffff', // Dark text for completed, white for others
        extendedProps: {
          description: task.notes,
          priority: task.priority,
          categoryId: task.categoryId,
          completed: task.completed
        }
      };
    });

  // Custom view data
  let weekViewData = [];
  let listViewData = [];
  let dayViewData = {};
  let currentWeekOffset = 0; // Track week navigation
  let currentDayOffset = 0; // Track day navigation

  // Check if Today button should be enabled
  let isCurrentMonthDisplayed = false;
  let currentDisplayedMonth = '';
  let currentDisplayedYear = '';

  // Week and Day view title data
  let currentWeekTitle = '';
  let currentDayTitle = '';

  $: isTodayButtonEnabled = (() => {
    if (currentView === 'dayGridMonth') {
      // For month view, check if current month is displayed
      return !isCurrentMonthDisplayed;
    } else if (currentView === 'timeGridWeek') {
      return currentWeekOffset !== 0;
    } else if (currentView === 'timeGridDay') {
      return currentDayOffset !== 0;
    }
    return false; // List view doesn't need Today button
  })();

  function checkCurrentMonth() {
    if (!calendar) {
      isCurrentMonthDisplayed = false;
      currentDisplayedMonth = '';
      currentDisplayedYear = '';
      return;
    }
    const calendarDate = calendar.getDate();
    const today = new Date();
    isCurrentMonthDisplayed = calendarDate.getFullYear() === today.getFullYear() &&
                             calendarDate.getMonth() === today.getMonth();

    // Update displayed month and year
    currentDisplayedMonth = calendarDate.toLocaleDateString('en-US', { month: 'long' });
    currentDisplayedYear = calendarDate.getFullYear().toString();
  }

  // Generate week view data (7 days with tasks)
  $: if (data.tasks) {
    const today = new Date();
    const startOfWeek = new Date(today);
    startOfWeek.setDate(today.getDate() - today.getDay() + (currentWeekOffset * 7)); // Start from Sunday + offset

    // Generate week title
    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6);

    const startMonth = startOfWeek.toLocaleDateString('en-US', { month: 'short' });
    const endMonth = endOfWeek.toLocaleDateString('en-US', { month: 'short' });
    const startDay = startOfWeek.getDate();
    const endDay = endOfWeek.getDate();
    const year = startOfWeek.getFullYear();

    if (startMonth === endMonth) {
      currentWeekTitle = `${startMonth} ${startDay} - ${endDay}, ${year}`;
    } else {
      currentWeekTitle = `${startMonth} ${startDay} - ${endMonth} ${endDay}, ${year}`;
    }

    weekViewData = Array.from({ length: 7 }, (_, i) => {
      const date = new Date(startOfWeek);
      date.setDate(startOfWeek.getDate() + i);
      const dateStr = date.toISOString().split('T')[0];

      const dayTasks = data.tasks.filter(task => {
        if (!task.dueDate) return false;
        // 使用正确的时区转换来比较日期
        const userTz = data.user?.timezone || 'Asia/Kuala_Lumpur';
        const { date: taskLocalDate } = fromUTCToParts(task.dueDate, userTz);
        return taskLocalDate === dateStr;
      }).sort((a, b) => b.priority - a.priority); // Sort by priority

      return {
        date: date,
        dateStr: dateStr,
        dayName: date.toLocaleDateString('en-US', { weekday: 'short' }),
        dayNumber: date.getDate(),
        isToday: dateStr === today.toISOString().split('T')[0],
        tasks: dayTasks
      };
    });

    // Generate list view data (next 20 tasks)
    const upcomingTasks = data.tasks
      .filter(task => task.dueDate && !task.completed)
      .sort((a, b) => {
        const dateA = new Date(a.dueDate);
        const dateB = new Date(b.dueDate);
        return dateA.getTime() - dateB.getTime();
      })
      .slice(0, 20);

    listViewData = upcomingTasks.map(task => ({
      ...task,
      formattedDate: formatUnifiedDate(task.dueDate, data.user?.timezone || 'Asia/Kuala_Lumpur', false)
    }));

    // Generate day view data (single day with tasks)
    const targetDate = new Date(today);
    targetDate.setDate(today.getDate() + currentDayOffset);
    const targetDateStr = targetDate.toISOString().split('T')[0];

    const dayTasks = data.tasks.filter(task => {
      if (!task.dueDate) return false;
      // 使用正确的时区转换来比较日期
      const userTz = data.user?.timezone || 'Asia/Kuala_Lumpur';
      const { date: taskLocalDate } = fromUTCToParts(task.dueDate, userTz);
      return taskLocalDate === targetDateStr;
    }).sort((a, b) => b.priority - a.priority);

    // Generate day title - shorter format for mobile
    const dayName = targetDate.toLocaleDateString('en-US', { weekday: 'short' });
    const monthDay = targetDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
    const dayYear = targetDate.getFullYear();
    currentDayTitle = `${dayName}, ${monthDay}, ${dayYear}`;

    dayViewData = {
      date: targetDate,
      dateStr: targetDateStr,
      dayName: targetDate.toLocaleDateString('en-US', { weekday: 'long' }),
      dayNumber: targetDate.getDate(),
      monthName: targetDate.toLocaleDateString('en-US', { month: 'long' }),
      year: targetDate.getFullYear(),
      isToday: targetDateStr === today.toISOString().split('T')[0],
      tasks: dayTasks
    };
  }

  onMount(() => {
    initializeCalendar();
  });

  onDestroy(() => {
    if (calendar) {
      calendar.destroy();
      calendar = null;
    }
  });

  // Update calendar events when data changes
  $: if (calendar && events && currentView === 'dayGridMonth') {
    calendar.removeAllEvents();
    calendar.addEventSource(events);
  }

  onDestroy(() => {
    if (calendar) {
      calendar.destroy();
    }
  });

  // Update events when data changes
  $: if (calendar && events) {
    calendar.removeAllEvents();
    calendar.addEventSource(events);
  }

  // Update calendar when view changes
  $: if (calendar && currentView) {
    calendar.changeView(currentView);
  }

  function handleViewChange(view: string) {
    currentView = view;
    if (view === 'dayGridMonth') {
      // Always destroy and recreate calendar for Month view to ensure it works
      if (calendar) {
        calendar.destroy();
        calendar = null;
      }
      // Wait for DOM to update, then recreate calendar
      setTimeout(() => {
        initializeCalendar();
      }, 100);
    }
  }

  function initializeCalendar() {
    if (calendarEl) {
      calendar = new Calendar(calendarEl, {
        plugins: [dayGridPlugin, timeGridPlugin, listPlugin, interactionPlugin],
        initialView: 'dayGridMonth',
        headerToolbar: false, // Custom header
        height: 'auto',
        events: events,
        eventClick: function(info) {
          goto(`/dashboard/tasks/${info.event.id}`);
        },
        dateClick: function(info) {
          const date = info.dateStr;
          goto(`/dashboard/tasks/new?date=${date}`);
        },
        eventDidMount: function(info) {
          if (info.event.extendedProps.description) {
            info.el.title = info.event.extendedProps.description;
          }

          if (info.event.extendedProps.completed) {
            info.el.style.opacity = '0.6';
            info.el.style.textDecoration = 'line-through';
            info.el.classList.add('completed-event');
          }

          // Remove priority icons - only use color differentiation
        },
        viewDidMount: function(info) {
          currentView = info.view.type;
          // Check if current month is displayed
          setTimeout(() => checkCurrentMonth(), 100);
        },
        // Mobile and responsive optimizations
        dayMaxEvents: 3,
        moreLinkClick: 'popover',
        eventDisplay: 'block'
      });

      calendar.render();
      // Initialize month display
      setTimeout(() => checkCurrentMonth(), 100);
    }
  }

  function goToToday() {
    if (currentView === 'timeGridWeek') {
      // Reset to current week
      currentWeekOffset = 0;
    } else if (currentView === 'timeGridDay') {
      // Reset to today
      currentDayOffset = 0;
    } else if (calendar) {
      calendar.today();
      setTimeout(() => checkCurrentMonth(), 100);
    }
  }

  function goToPrev() {
    if (currentView === 'timeGridWeek') {
      // Custom week view navigation
      currentWeekOffset--;
    } else if (currentView === 'timeGridDay') {
      // Custom day view navigation
      currentDayOffset--;
    } else if (calendar) {
      calendar.prev();
      setTimeout(() => checkCurrentMonth(), 100);
    }
  }

  function goToNext() {
    if (currentView === 'timeGridWeek') {
      // Custom week view navigation
      currentWeekOffset++;
    } else if (currentView === 'timeGridDay') {
      // Custom day view navigation
      currentDayOffset++;
    } else if (calendar) {
      calendar.next();
      setTimeout(() => checkCurrentMonth(), 100);
    }
  }
</script>

<svelte:head>
  <title>Calendar - Routine Mail</title>
</svelte:head>

<PageTransition>
  <div class="calendar-page">
    <!-- Mobile Header - Optimized Layout -->
    <div class="lg:hidden mb-4">
      <!-- Mobile Navigation - Centered Month/Year -->
      <div class="mobile-nav-container">
        <button
          class="mobile-nav-btn"
          on:click={goToPrev}
          aria-label="Previous period"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
          </svg>
        </button>

        <div class="mobile-title-container">
          {#if currentView === 'dayGridMonth' && currentDisplayedMonth && currentDisplayedYear}
            <div class="mobile-month-display">
              <span class="month-name">{currentDisplayedMonth}</span>
              <span class="year-name">{currentDisplayedYear}</span>
            </div>
          {:else if currentView === 'timeGridWeek' && currentWeekTitle}
            <div class="mobile-week-display">
              <span class="week-title">{currentWeekTitle}</span>
            </div>
          {:else if currentView === 'timeGridDay' && currentDayTitle}
            <div class="mobile-day-display">
              <span class="day-title">{currentDayTitle}</span>
            </div>
          {/if}
        </div>

        <button
          class="mobile-nav-btn"
          on:click={goToNext}
          aria-label="Next period"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
          </svg>
        </button>
      </div>

      <!-- Mobile View Selector with Today Button -->
      <div class="mobile-controls-row">
        <div class="mobile-view-selector">
          <button
            class="mobile-view-btn {currentView === 'dayGridMonth' ? 'active' : ''}"
            on:click={() => handleViewChange('dayGridMonth')}
          >
            Month
          </button>
          <button
            class="mobile-view-btn {currentView === 'timeGridWeek' ? 'active' : ''}"
            on:click={() => handleViewChange('timeGridWeek')}
          >
            Week
          </button>
          <button
            class="mobile-view-btn {currentView === 'timeGridDay' ? 'active' : ''}"
            on:click={() => handleViewChange('timeGridDay')}
          >
            Day
          </button>
          <button
            class="mobile-view-btn {currentView === 'listWeek' ? 'active' : ''}"
            on:click={() => handleViewChange('listWeek')}
          >
            List
          </button>
        </div>
        <button
          class="mobile-today-btn-icon {!isTodayButtonEnabled ? 'disabled' : ''}"
          on:click={goToToday}
          disabled={!isTodayButtonEnabled}
          title="Go to Today"
          aria-label="Go to Today"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
          </svg>
        </button>
      </div>
    </div>

    <!-- Desktop Header -->
    <div class="hidden lg:block mb-8">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold text-slate-900">Calendar</h1>
          <p class="text-slate-600 mt-1">View and manage your scheduled tasks</p>
        </div>
        <div class="flex items-center gap-6">
          <!-- Navigation Controls -->
          <div class="flex items-center gap-6">
            <!-- Month Navigation -->
            <div class="flex items-center gap-3">
              <button
                class="desktop-nav-btn"
                on:click={goToPrev}
                aria-label="Previous period"
              >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
              </button>

              {#if currentView === 'dayGridMonth' && currentDisplayedMonth && currentDisplayedYear}
                <div class="desktop-month-display">
                  <span class="month-name">{currentDisplayedMonth}</span>
                  <span class="year-name">{currentDisplayedYear}</span>
                </div>
              {:else if currentView === 'timeGridWeek' && currentWeekTitle}
                <div class="desktop-week-display">
                  <span class="week-title">{currentWeekTitle}</span>
                </div>
              {:else if currentView === 'timeGridDay' && currentDayTitle}
                <div class="desktop-day-display">
                  <span class="day-title">{currentDayTitle}</span>
                </div>
              {/if}

              <button
                class="desktop-nav-btn"
                on:click={goToNext}
                aria-label="Next period"
              >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              </button>
            </div>

            <!-- Today Button - Separate -->
            <button
              class="desktop-today-btn {!isTodayButtonEnabled ? 'disabled' : ''}"
              on:click={goToToday}
              disabled={!isTodayButtonEnabled}
            >
              Today
            </button>
          </div>

          <!-- Desktop View Selector -->
          <div class="desktop-view-selector">
            <button
              class="desktop-view-btn {currentView === 'dayGridMonth' ? 'active' : ''}"
              on:click={() => handleViewChange('dayGridMonth')}
            >
              Month
            </button>
            <button
              class="desktop-view-btn {currentView === 'timeGridWeek' ? 'active' : ''}"
              on:click={() => handleViewChange('timeGridWeek')}
            >
              Week
            </button>
            <button
              class="desktop-view-btn {currentView === 'timeGridDay' ? 'active' : ''}"
              on:click={() => handleViewChange('timeGridDay')}
            >
              Day
            </button>
            <button
              class="desktop-view-btn {currentView === 'listWeek' ? 'active' : ''}"
              on:click={() => handleViewChange('listWeek')}
            >
              List
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Calendar Container -->
    <div class="modern-calendar-container">
      <!-- Standard Calendar Views -->
      {#if currentView === 'dayGridMonth'}
        <div bind:this={calendarEl} class="calendar-wrapper"></div>
      {/if}

      <!-- Custom Week View -->
      {#if currentView === 'timeGridWeek'}
        <div class="custom-week-view">
          <h3 class="week-title">
            {#if currentWeekOffset === 0}
              This Week's Tasks
            {:else if currentWeekOffset === -1}
              Last Week's Tasks
            {:else if currentWeekOffset === 1}
              Next Week's Tasks
            {:else if currentWeekOffset < 0}
              {Math.abs(currentWeekOffset)} Weeks Ago
            {:else}
              {currentWeekOffset} Weeks Ahead
            {/if}
          </h3>
          <div class="week-grid">
            {#each weekViewData as day}
              <div class="week-day-card {day.isToday ? 'today' : ''}">
                <div class="day-header">
                  <span class="day-name">{day.dayName}</span>
                  <span class="day-number">{day.dayNumber}</span>
                </div>
                <div class="day-tasks">
                  {#each day.tasks as task}
                    <div
                      class="week-task priority-{task.priority} {task.completed ? 'completed' : ''}"
                      on:click={() => goto(`/dashboard/tasks/${task.id}`)}
                      role="button"
                      tabindex="0"
                      on:keydown={(e) => e.key === 'Enter' && goto(`/dashboard/tasks/${task.id}`)}
                    >
                      <span class="task-title">{task.title}</span>
                    </div>
                  {/each}
                  {#if day.tasks.length === 0}
                    <div class="no-tasks">No tasks</div>
                  {/if}
                </div>
              </div>
            {/each}
          </div>
        </div>
      {/if}

      <!-- Custom Day View -->
      {#if currentView === 'timeGridDay'}
        <div class="custom-day-view">
          <h3 class="day-title">
            {#if currentDayOffset === 0}
              Today's Tasks
            {:else if currentDayOffset === -1}
              Yesterday's Tasks
            {:else if currentDayOffset === 1}
              Tomorrow's Tasks
            {:else if currentDayOffset < 0}
              {Math.abs(currentDayOffset)} Days Ago
            {:else}
              {currentDayOffset} Days Ahead
            {/if}
          </h3>

          <div class="day-header-info">
            <div class="day-date">
              <span class="day-name">{dayViewData.dayName || ''}</span>
              <span class="day-number">{dayViewData.dayNumber || ''}</span>
              <span class="month-year">{dayViewData.monthName || ''} {dayViewData.year || ''}</span>
            </div>
          </div>

          <div class="day-tasks">
            {#each (dayViewData.tasks || []) as task}
              <div
                class="day-task priority-{task.priority} {task.completed ? 'completed' : ''}"
                on:click={() => goto(`/dashboard/tasks/${task.id}`)}
                role="button"
                tabindex="0"
                on:keydown={(e) => e.key === 'Enter' && goto(`/dashboard/tasks/${task.id}`)}
              >
                <div class="task-content">
                  <div class="task-title">{task.title}</div>
                  {#if task.notes}
                    <div class="task-notes">{task.notes}</div>
                  {/if}
                </div>
              </div>
            {/each}
            {#if (dayViewData.tasks || []).length === 0}
              <div class="no-tasks-day">No tasks for this day</div>
            {/if}
          </div>
        </div>
      {/if}

      <!-- Custom List View -->
      {#if currentView === 'listWeek'}
        <div class="custom-list-view">
          <h3 class="list-title">Upcoming Tasks</h3>
          <div class="task-list">
            {#each listViewData as task}
              <div
                class="list-task priority-{task.priority} {task.completed ? 'completed' : ''}"
                on:click={() => goto(`/dashboard/tasks/${task.id}`)}
                role="button"
                tabindex="0"
                on:keydown={(e) => e.key === 'Enter' && goto(`/dashboard/tasks/${task.id}`)}
              >
                <div class="task-date">
                  <span class="date-text">{task.formattedDate}</span>
                </div>
                <div class="task-content">
                  <div class="task-header">
                    <span class="task-title">{task.title}</span>
                  </div>
                  {#if task.notes}
                    <div class="task-notes">{task.notes}</div>
                  {/if}
                </div>
              </div>
            {/each}
            {#if listViewData.length === 0}
              <div class="no-tasks-list">No upcoming tasks</div>
            {/if}
          </div>
        </div>
      {/if}
    </div>

    <!-- Legend -->
    <div class="mt-6 flex flex-wrap items-center gap-4 text-sm text-gray-600">
      <div class="flex items-center gap-2">
        <div class="w-3 h-3 bg-red-500 rounded"></div>
        <span>High Priority</span>
      </div>
      <div class="flex items-center gap-2">
        <div class="w-3 h-3 bg-yellow-500 rounded"></div>
        <span>Medium Priority</span>
      </div>
      <div class="flex items-center gap-2">
        <div class="w-3 h-3 bg-green-500 rounded"></div>
        <span>Low Priority</span>
      </div>
      <div class="flex items-center gap-2">
        <div class="w-3 h-3 bg-gray-400 rounded opacity-60"></div>
        <span>Completed</span>
      </div>
    </div>
  </div>
</PageTransition>

<style>
  .calendar-page {
    @apply max-w-7xl mx-auto;
    width: 100%;
    max-width: 100vw;
    overflow-x: hidden;
    padding: 0 1rem;
  }

  @media (max-width: 768px) {
    .calendar-page {
      padding: 0 0.75rem;
    }
  }



  /* Modern Calendar Container - Office Style with Enhanced Design */
  .modern-calendar-container {
    background: #ffffff;
    border-radius: 24px;
    border: 1px solid #e5e7eb;
    padding: 2.5rem;
    box-shadow:
      0 10px 25px -5px rgba(0, 0, 0, 0.08),
      0 4px 10px -3px rgba(0, 0, 0, 0.04),
      inset 0 1px 0 rgba(255, 255, 255, 0.95);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: visible; /* Allow popovers to escape container bounds */
  }

  /* Subtle geometric pattern background */
  .modern-calendar-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
      radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.02) 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, rgba(16, 185, 129, 0.02) 0%, transparent 50%),
      radial-gradient(circle at 40% 60%, rgba(245, 158, 11, 0.01) 0%, transparent 50%);
    pointer-events: none;
  }

  /* Top accent line */
  .modern-calendar-container::after {
    content: '';
    position: absolute;
    top: 0;
    left: 10%;
    right: 10%;
    height: 2px;
    background: linear-gradient(90deg, transparent, #3b82f6, transparent);
    border-radius: 0 0 2px 2px;
  }

  .modern-calendar-container:hover {
    box-shadow:
      0 15px 35px -8px rgba(0, 0, 0, 0.12),
      0 8px 20px -5px rgba(0, 0, 0, 0.08),
      inset 0 1px 0 rgba(255, 255, 255, 1);
    transform: translateY(-3px);
  }

  .calendar-wrapper {
    @apply min-h-[600px];
  }

  /* Mobile Navigation Buttons */
  .mobile-nav-btn {
    @apply w-12 h-12 flex items-center justify-center text-slate-700 bg-white rounded-xl border border-slate-200 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500/20;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    flex-shrink: 0; /* Prevent buttons from shrinking */
  }

  .mobile-nav-btn:hover {
    background: #f1f5f9;
    border-color: #cbd5e1;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  .mobile-today-btn {
    @apply px-6 py-3 text-sm font-semibold text-white rounded-xl transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-emerald-500/20;
    background: linear-gradient(135deg, #10b981, #059669);
    box-shadow:
      0 2px 8px rgba(16, 185, 129, 0.25),
      0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
  }

  .mobile-today-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
  }

  .mobile-today-btn:hover:not(.disabled)::before {
    left: 100%;
  }

  .mobile-today-btn:hover:not(.disabled) {
    background: linear-gradient(135deg, #059669, #047857);
    transform: translateY(-2px);
    box-shadow:
      0 4px 12px rgba(16, 185, 129, 0.35),
      0 2px 6px rgba(0, 0, 0, 0.15);
  }

  .mobile-today-btn.disabled {
    background: linear-gradient(135deg, #94a3b8, #64748b);
    cursor: not-allowed;
    opacity: 0.6;
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Mobile Today Button - Icon Only */
  .mobile-today-btn-icon {
    @apply w-12 h-12 flex items-center justify-center text-emerald-700 bg-emerald-50 rounded-2xl border border-emerald-200 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-emerald-500/20;
    flex-shrink: 0; /* Prevent shrinking */
  }

  .mobile-today-btn-icon:hover:not(.disabled) {
    @apply bg-emerald-100 border-emerald-300 transform -translate-y-0.5;
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.15);
  }

  .mobile-today-btn-icon.disabled {
    @apply bg-gray-50 text-gray-400 border-gray-200 cursor-not-allowed;
  }

  /* Mobile Navigation Container */
  .mobile-nav-container {
    @apply flex items-center justify-between mb-4;
    width: 100%;
    max-width: 100vw;
    overflow: hidden;
  }

  /* Mobile Title Container */
  .mobile-title-container {
    @apply flex flex-col items-center justify-center;
    flex: 1;
    min-width: 0;
    max-width: calc(100vw - 120px); /* Reserve space for nav buttons */
    padding: 0 8px;
    height: 48px; /* Match button height */
  }

  /* Mobile Month Display */
  .mobile-month-display {
    @apply text-center;
  }

  .mobile-month-display .month-name {
    @apply text-lg font-bold text-slate-800 block;
  }

  .mobile-month-display .year-name {
    @apply text-sm font-medium text-slate-600;
  }

  /* Mobile Week Display */
  .mobile-week-display {
    @apply text-center w-full;
  }

  .mobile-week-display .week-title {
    @apply text-sm font-semibold text-slate-800;
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
  }

  /* Mobile Day Display */
  .mobile-day-display {
    @apply text-center w-full;
  }

  .mobile-day-display .day-title {
    @apply text-sm font-semibold text-slate-800;
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
  }

  /* Desktop Navigation Buttons */
  .desktop-nav-btn {
    @apply w-10 h-10 flex items-center justify-center text-slate-700 bg-white rounded-lg border border-slate-200 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500/20;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  }

  .desktop-nav-btn:hover {
    background: #f1f5f9;
    border-color: #cbd5e1;
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  }

  .desktop-today-btn {
    @apply px-4 py-2 text-sm font-semibold text-white rounded-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-emerald-500/20;
    background: linear-gradient(135deg, #10b981, #059669);
    box-shadow:
      0 1px 6px rgba(16, 185, 129, 0.2),
      0 1px 2px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.15);
    position: relative;
    overflow: hidden;
  }

  .desktop-today-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.15), transparent);
    transition: left 0.4s ease;
  }

  .desktop-today-btn:hover:not(.disabled)::before {
    left: 100%;
  }

  .desktop-today-btn:hover:not(.disabled) {
    background: linear-gradient(135deg, #059669, #047857);
    transform: translateY(-1px);
    box-shadow:
      0 2px 8px rgba(16, 185, 129, 0.3),
      0 1px 4px rgba(0, 0, 0, 0.12);
  }

  .desktop-today-btn.disabled {
    background: linear-gradient(135deg, #94a3b8, #64748b);
    cursor: not-allowed;
    opacity: 0.6;
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Desktop Month Display - Two Lines */
  .desktop-month-display {
    @apply flex flex-col items-center justify-center gap-0 px-4 py-2 bg-slate-50 rounded-lg border border-slate-200;
    min-height: 48px;
  }

  .desktop-month-display .month-name {
    @apply text-lg font-bold text-slate-800 leading-tight;
  }

  .desktop-month-display .year-name {
    @apply text-sm font-medium text-slate-600 leading-tight;
  }

  /* Desktop Week Display */
  .desktop-week-display {
    @apply flex items-center justify-center px-4 py-2 bg-slate-50 rounded-lg border border-slate-200;
    min-height: 48px; /* Match month display height */
  }

  .desktop-week-display .week-title {
    @apply text-lg font-semibold text-slate-800;
  }

  /* Desktop Day Display */
  .desktop-day-display {
    @apply flex items-center justify-center px-4 py-2 bg-slate-50 rounded-lg border border-slate-200;
    min-height: 48px; /* Match month display height */
  }

  .desktop-day-display .day-title {
    @apply text-lg font-semibold text-slate-800;
  }

  /* Mobile Controls Row */
  .mobile-controls-row {
    @apply flex items-center gap-3;
    width: 100%;
    max-width: 100vw;
    overflow: hidden;
  }

  /* Mobile View Selector */
  .mobile-view-selector {
    @apply flex bg-slate-100 rounded-2xl p-1 gap-1;
    flex: 1;
    min-width: 0; /* Allow shrinking */
    max-width: calc(100vw - 80px); /* Reserve space for icon-only Today button */
  }

  .mobile-view-btn {
    @apply flex-1 px-3 py-3 text-sm font-medium text-slate-600 rounded-xl transition-all duration-200 focus:outline-none text-center;
    background: transparent;
    border: none;
    cursor: pointer;
    min-width: 0; /* Allow text to shrink */
    white-space: nowrap;
  }

  .mobile-view-btn:hover {
    background: rgba(255, 255, 255, 0.7);
    color: #334155;
  }

  .mobile-view-btn.active {
    background: #ffffff;
    color: #1e293b;
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  /* Desktop View Selector */
  .desktop-view-selector {
    @apply flex bg-slate-100 rounded-xl p-1 gap-1;
  }

  .desktop-view-btn {
    @apply px-4 py-2 text-sm font-medium text-slate-600 rounded-lg transition-all duration-200 focus:outline-none;
    background: transparent;
    border: none;
    cursor: pointer;
  }

  .desktop-view-btn:hover {
    background: rgba(255, 255, 255, 0.7);
    color: #334155;
  }

  .desktop-view-btn.active {
    background: #ffffff;
    color: #1e293b;
    font-weight: 600;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  /* FullCalendar Base Styling */
  :global(.fc) {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background: transparent;
  }

  :global(.fc-header-toolbar) {
    display: none !important;
  }

  /* Day Grid Office Style - Enhanced with Patterns */
  :global(.fc-daygrid-day) {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
  }

  /* Subtle dot pattern for texture */
  :global(.fc-daygrid-day::before) {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: radial-gradient(circle at 1px 1px, rgba(156, 163, 175, 0.1) 1px, transparent 0);
    background-size: 20px 20px;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  :global(.fc-daygrid-day:hover) {
    background: #f9fafb;
    border-color: #3b82f6;
    cursor: pointer;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }

  :global(.fc-daygrid-day:hover::before) {
    opacity: 1;
  }



  :global(.fc-day-today::before) {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(34, 197, 94, 0.08) 0%, transparent 70%);
    pointer-events: none;
  }

  :global(.fc-day-past) {
    background: #f1f5f9 !important;
    /* 移除 opacity，避免影响任务颜色 */
  }

  :global(.fc-day-past .fc-daygrid-day-number) {
    color: #9ca3af !important;
    font-weight: 400 !important;
  }

  /* 确保过去日期的任务保持原色 */
  :global(.fc-day-past .fc-event) {
    opacity: 1 !important;
  }

  /* 今天的样式 - 放在最后确保优先级最高 */
  :global(.fc-day-today),
  :global(.fc-day-today .fc-daygrid-day-frame),
  :global(.fc-day-today .fc-daygrid-day-bg) {
    background: #e8f5e8 !important;
    background-color: #e8f5e8 !important;
  }

  :global(.fc-day-today) {
    border: 1px solid rgba(34, 197, 94, 0.4) !important;
    box-shadow:
      0 0 0 1px rgba(34, 197, 94, 0.3),
      0 4px 12px rgba(34, 197, 94, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.9) !important;
    position: relative;
  }

  :global(.fc-day-future) {
    background: #ffffff;
  }

  /* Day Numbers */
  :global(.fc-daygrid-day-number) {
    color: #475569;
    font-weight: 500;
    padding: 12px;
    text-decoration: none;
    font-size: 14px;
  }

  :global(.fc-day-today .fc-daygrid-day-number) {
    color: #1e293b;
    font-weight: 600;
    background: #ffffff;
    border-radius: 50%;
    width: 22px;
    height: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 6px;
    box-shadow:
      0 1px 4px rgba(0, 0, 0, 0.1),
      0 1px 2px rgba(0, 0, 0, 0.06);
    border: 1px solid #22c55e;
    font-size: 12px;
  }

  /* 手机端专用样式 - 更小的圆圈 */
  @media (max-width: 768px) {
    :global(.fc-day-today .fc-daygrid-day-number) {
      width: 18px !important;
      height: 18px !important;
      font-size: 10px !important;
      margin: 4px !important;
      border-width: 0.5px !important;
    }
  }

  /* Modern Event Styling - Office Style with Icons */
  :global(.fc-event) {
    border-radius: 8px !important;
    padding: 6px 8px !important;
    margin: 2px 1px !important;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
      0 2px 4px rgba(0, 0, 0, 0.08),
      0 1px 2px rgba(0, 0, 0, 0.06);
    font-weight: 500 !important;
    min-height: 28px !important;
    display: flex !important;
    align-items: center !important;
    position: relative;
    overflow: hidden;
    border-left: 3px solid currentColor !important;
    /* DO NOT override background or text colors - let FullCalendar handle them */
  }

  /* Subtle inner glow effect */
  :global(.fc-event::before) {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%);
    pointer-events: none;
    border-radius: inherit;
  }

  /* Priority indicator (will be added via JavaScript) */
  :global(.fc-event::after) {
    content: '';
    position: absolute;
    top: 4px;
    right: 4px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.8);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  :global(.fc-event:hover) {
    transform: translateY(-1px);
    box-shadow:
      0 4px 12px rgba(0, 0, 0, 0.12),
      0 2px 6px rgba(0, 0, 0, 0.08);
  }

  :global(.fc-event:hover::after) {
    opacity: 1;
  }

  :global(.fc-daygrid-event) {
    font-size: 13px !important;
    font-weight: 500 !important;
    line-height: 1.2 !important;
  }

  :global(.fc-event-title) {
    font-weight: 500 !important;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    /* DO NOT override color - let FullCalendar handle it */
  }

  /* Completed tasks styling */
  :global(.fc-event.completed-event) {
    opacity: 0.7;
    text-decoration: line-through;
  }

  /* List View Styling - Fixed Layout */
  :global(.fc-list-table) {
    border-collapse: separate !important;
    border-spacing: 0 4px !important;
  }

  :global(.fc-list-event) {
    background: #ffffff !important;
    border-radius: 8px !important;
    margin: 0 !important;
    border: 1px solid #e2e8f0 !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
  }

  :global(.fc-list-event:hover) {
    background: #f8fafc !important;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  }

  :global(.fc-list-event-time) {
    padding: 12px !important;
    font-weight: 500 !important;
    color: #64748b !important;
    border-right: 1px solid #e2e8f0 !important;
    width: 120px !important;
  }

  :global(.fc-list-event-title) {
    padding: 12px !important;
    font-weight: 500 !important;
    color: #1e293b !important;
  }

  :global(.fc-list-event-dot) {
    width: 8px !important;
    height: 8px !important;
    border-radius: 50% !important;
    margin-right: 8px !important;
  }

  /* Week and Time Grid Styling */
  :global(.fc-timegrid-slot) {
    border-color: #f1f5f9 !important;
  }

  :global(.fc-timegrid-axis) {
    color: #64748b;
    font-size: 12px;
    font-weight: 500;
  }

  :global(.fc-col-header-cell) {
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 50%, #f8fafc 100%) !important;
    border-color: rgba(226, 232, 240, 0.8) !important;
    color: #475569;
    font-weight: 700;
    padding: 18px 8px;
    text-transform: uppercase;
    font-size: 11px;
    letter-spacing: 1px;
    position: relative;
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.8);
  }

  :global(.fc-col-header-cell::after) {
    content: '';
    position: absolute;
    bottom: 0;
    left: 20%;
    right: 20%;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.3), transparent);
  }

  /* More Link Styling */
  :global(.fc-more-link) {
    color: #475569 !important;
    font-weight: 600 !important;
    text-decoration: none !important;
    padding: 4px 8px !important;
    border-radius: 6px !important;
    background: #f1f5f9 !important;
    transition: all 0.2s ease !important;
    font-size: 12px !important;
  }

  :global(.fc-more-link:hover) {
    background: #e2e8f0 !important;
    transform: translateY(-1px) !important;
  }

  /* Popover Styling - Fix transparency issue and boundary overflow */
  :global(.fc-popover) {
    background: #ffffff !important;
    border: 1px solid #e5e7eb !important;
    border-radius: 12px !important;
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 4px 10px -3px rgba(0, 0, 0, 0.05) !important;
    padding: 0 !important;
    z-index: 2147483647 !important; /* Ensure the popover stays on top */
    backdrop-filter: blur(8px) !important;
    /* Fix boundary overflow - more precise adjustment */
    max-width: 250px !important;
  }

  /* Adjust popover position for rightmost cells to prevent overflow */
  :global(.fc-daygrid-day:last-child .fc-popover) {
    transform: translateX(-85%) !important;
    margin-left: -20px !important;
  }

  /* Also adjust for second-to-last cell on smaller screens */
  @media (max-width: 768px) {
    :global(.fc-daygrid-day:nth-last-child(-n+2) .fc-popover) {
      transform: translateX(-75%) !important;
      margin-left: -15px !important;
    }
  }

  :global(.fc-popover-header) {
    background: #f8fafc !important;
    border-bottom: 1px solid #e5e7eb !important;
    padding: 8px 12px !important;
    border-radius: 12px 12px 0 0 !important;
    font-weight: 600 !important;
    color: #1e293b !important;
    font-size: 13px !important;
  }

  :global(.fc-popover-body) {
    padding: 8px !important;
    background: #ffffff !important;
    border-radius: 0 0 12px 12px !important;
  }

  :global(.fc-popover .fc-event) {
    margin: 2px 0 !important;
    border-radius: 6px !important;
  }

  /* Mobile Responsive Optimizations - Maximized Text Display */
  @media (max-width: 768px) {
    .modern-calendar-container {
      padding: 1rem;
      border-radius: 20px;
      margin: 0 -0.5rem;
    }

    /* Mobile events - maximized text space */
    :global(.fc-daygrid-event) {
      font-size: 10px !important;
      min-height: 24px !important;
      padding: 1px 3px !important;
      overflow: hidden !important;
      margin: 0.5px !important;
    }

    :global(.fc-event-title) {
      font-size: 10px !important;
      line-height: 1.1 !important;
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      white-space: nowrap !important;
      max-width: 100% !important;
      font-weight: 600 !important;
    }

    :global(.fc-daygrid-day-number) {
      padding: 4px;
      font-size: 13px;
      font-weight: 600;
    }

    :global(.fc-day-today .fc-daygrid-day-number) {
      width: 28px;
      height: 28px;
      font-size: 13px;
    }

    /* Better spacing for mobile */
    :global(.fc-daygrid-day) {
      min-height: 65px;
    }

    /* Mobile List View Optimization */
    :global(.fc-list-event-time) {
      width: 70px !important;
      font-size: 11px !important;
      padding: 6px !important;
    }

    :global(.fc-list-event-title) {
      padding: 6px !important;
      font-size: 13px !important;
    }

    /* Mobile Week View - Better Column Width */
    :global(.fc-timegrid-col) {
      min-width: 100px !important;
    }
  }

  /* Extra small screens - even more compact */
  @media (max-width: 480px) {
    :global(.fc-daygrid-event) {
      font-size: 9px !important;
      padding: 0.5px 2px !important;
      min-height: 20px !important;
      margin: 0.25px !important;
    }

    :global(.fc-event-title) {
      font-size: 9px !important;
      line-height: 1.0 !important;
    }

    :global(.fc-daygrid-day-number) {
      font-size: 12px;
      padding: 3px;
    }

    :global(.fc-daygrid-day) {
      min-height: 55px;
    }

    /* Very small screens - ultra compact list view */
    :global(.fc-list-event-time) {
      width: 55px !important;
      font-size: 10px !important;
      padding: 4px !important;
    }

    :global(.fc-list-event-title) {
      font-size: 12px !important;
      padding: 4px !important;
    }
  }



  /* FullCalendar Modern Styling */
  :global(.fc) {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background: transparent;
  }

  /* Calendar Header */
  :global(.fc-header-toolbar) {
    display: none !important; /* We use custom header */
  }

  /* Day Grid Styling */
  :global(.fc-daygrid-day) {
    background: rgba(255, 255, 255, 0.5);
    border: 1px solid rgba(226, 232, 240, 0.3);
    transition: all 0.2s ease;
  }

  :global(.fc-daygrid-day:hover) {
    background: rgba(248, 250, 252, 0.8);
    cursor: pointer;
  }

  :global(.fc-day-today) {
    background: rgba(59, 130, 246, 0.05) !important;
    border-color: rgba(59, 130, 246, 0.2) !important;
  }

  :global(.fc-day-past) {
    background: rgba(248, 250, 252, 0.3);
    color: #94a3b8;
  }

  :global(.fc-day-future) {
    background: rgba(255, 255, 255, 0.7);
  }

  /* Day Numbers */
  :global(.fc-daygrid-day-number) {
    color: #334155;
    font-weight: 500;
    padding: 8px;
    text-decoration: none;
  }

  :global(.fc-day-today .fc-daygrid-day-number) {
    color: #1e40af;
    font-weight: 600;
  }

  /* Modern Event Styling */
  :global(.fc-event) {
    border: none !important;
    border-radius: 8px !important;
    padding: 4px 8px !important;
    margin: 2px !important;
    cursor: pointer;
    transition: all 0.2s ease;
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  :global(.fc-event:hover) {
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }



  :global(.fc-event.completed-event) {
    opacity: 0.6;
    text-decoration: line-through;
    background: rgba(148, 163, 184, 0.2) !important;
  }

  :global(.fc-daygrid-event) {
    font-size: 12px !important;
    font-weight: 500 !important;
    line-height: 1.3 !important;
  }

  :global(.fc-event-title) {
    font-weight: 500;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  /* List View Styling */
  :global(.fc-list-event) {
    background: rgba(255, 255, 255, 0.8) !important;
    border-radius: 8px !important;
    margin: 4px 0 !important;
    border-left: 4px solid currentColor !important;
  }

  :global(.fc-list-event:hover) {
    background: rgba(248, 250, 252, 0.9) !important;
    transform: translateX(4px);
  }

  /* Week and Time Grid Styling */
  :global(.fc-timegrid-slot) {
    border-color: rgba(226, 232, 240, 0.3) !important;
  }

  :global(.fc-timegrid-axis) {
    color: #64748b;
    font-size: 12px;
  }

  :global(.fc-col-header-cell) {
    background: rgba(248, 250, 252, 0.8) !important;
    border-color: rgba(226, 232, 240, 0.3) !important;
    color: #334155;
    font-weight: 600;
    padding: 12px 8px;
  }

  /* Scrollbars */
  :global(.fc-scroller::-webkit-scrollbar) {
    width: 6px;
    height: 6px;
  }

  :global(.fc-scroller::-webkit-scrollbar-track) {
    background: rgba(248, 250, 252, 0.5);
    border-radius: 3px;
  }

  :global(.fc-scroller::-webkit-scrollbar-thumb) {
    background: rgba(148, 163, 184, 0.5);
    border-radius: 3px;
  }

  :global(.fc-scroller::-webkit-scrollbar-thumb:hover) {
    background: rgba(100, 116, 139, 0.7);
  }

  /* More Link Styling */
  :global(.fc-more-link) {
    color: #475569 !important;
    font-weight: 500 !important;
    text-decoration: none !important;
    padding: 2px 6px !important;
    border-radius: 4px !important;
    background: rgba(248, 250, 252, 0.8) !important;
    transition: all 0.2s ease !important;
  }

  :global(.fc-more-link:hover) {
    background: rgba(226, 232, 240, 0.8) !important;
    transform: translateY(-1px) !important;
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .calendar-container {
      padding: 1rem;
      border-radius: 16px;
    }

    :global(.fc-daygrid-event) {
      font-size: 11px !important;
    }

    :global(.fc-daygrid-day-number) {
      padding: 4px;
      font-size: 14px;
    }
  }

  /* Custom Week View Styling */
  .custom-week-view {
    padding: 1rem 0;
  }

  .week-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1.5rem;
    text-align: center;
  }

  .week-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1rem;
  }

  /* Responsive grid columns */
  @media (max-width: 1200px) {
    .week-grid {
      grid-template-columns: repeat(4, 1fr);
    }
  }

  @media (max-width: 900px) {
    .week-grid {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  .week-day-card {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 1rem;
    transition: all 0.3s ease;
    min-height: 200px;
    max-height: 400px;
    display: flex;
    flex-direction: column;
  }

  .week-day-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transform: translateY(-2px);
  }

  .week-day-card.today {
    background: #eff6ff;
    border-color: #3b82f6;
    box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.2);
  }

  .day-header {
    text-align: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e5e7eb;
  }

  .day-name {
    display: block;
    font-size: 0.875rem;
    font-weight: 600;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .day-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: #1f2937;
    margin-top: 0.25rem;
  }

  .today .day-number {
    color: #3b82f6;
  }

  .day-tasks {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    flex: 1;
    overflow-y: auto;
    max-height: 280px;
    padding-right: 4px;
  }

  /* Custom scrollbar for day-tasks */
  .day-tasks::-webkit-scrollbar {
    width: 4px;
  }

  .day-tasks::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 2px;
  }

  .day-tasks::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 2px;
  }

  .day-tasks::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }

  .week-task {
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
    flex-shrink: 0;
  }

  .week-task:hover {
    background: #f3f4f6;
    transform: translateX(2px);
  }

  .week-task.priority-3 {
    border-left: 3px solid #dc2626;
  }

  .week-task.priority-2 {
    border-left: 3px solid #ea580c;
  }

  .week-task.priority-1 {
    border-left: 3px solid #059669;
  }

  .week-task.completed {
    opacity: 0.6;
    text-decoration: line-through;
  }

  .task-priority-icon {
    font-size: 0.75rem;
  }

  .task-title {
    font-size: 0.8rem;
    font-weight: 500;
    color: #374151;
    flex: 1;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    word-break: break-word;
  }

  /* Responsive task title sizing */
  @media (max-width: 1200px) {
    .task-title {
      font-size: 0.75rem;
      -webkit-line-clamp: 3;
    }
  }

  @media (max-width: 900px) {
    .task-title {
      font-size: 0.8rem;
      -webkit-line-clamp: 2;
    }
  }

  .no-tasks {
    text-align: center;
    color: #9ca3af;
    font-style: italic;
    padding: 1rem 0;
  }

  /* Custom Day View Styling */
  .custom-day-view {
    padding: 1rem 0;
  }

  .day-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1.5rem;
    text-align: center;
  }

  .day-header-info {
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    text-align: center;
  }

  .day-date {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
  }

  .day-name {
    font-size: 1.125rem;
    font-weight: 600;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 1px;
  }

  .day-number {
    font-size: 3rem;
    font-weight: 700;
    color: #1f2937;
    line-height: 1;
  }

  .month-year {
    font-size: 1rem;
    font-weight: 500;
    color: #6b7280;
  }

  .day-tasks {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .day-task {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .day-task:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transform: translateY(-2px);
  }

  .day-task.priority-3 {
    border-left: 4px solid #dc2626;
  }

  .day-task.priority-2 {
    border-left: 4px solid #ea580c;
  }

  .day-task.priority-1 {
    border-left: 4px solid #059669;
  }

  .day-task.completed {
    opacity: 0.6;
    text-decoration: line-through;
  }

  .day-task .task-content {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .day-task .task-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
  }

  .day-task .task-notes {
    font-size: 0.875rem;
    color: #6b7280;
    line-height: 1.5;
  }

  .no-tasks-day {
    text-align: center;
    color: #9ca3af;
    font-style: italic;
    padding: 2rem 0;
    font-size: 1.125rem;
  }

  /* Custom List View Styling */
  .custom-list-view {
    padding: 1rem 0;
  }

  .list-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1.5rem;
    text-align: center;
  }

  .task-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .list-task {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    gap: 1rem;
  }

  .list-task:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);
  }

  .list-task.priority-3 {
    border-left: 4px solid #dc2626;
  }

  .list-task.priority-2 {
    border-left: 4px solid #ea580c;
  }

  .list-task.priority-1 {
    border-left: 4px solid #059669;
  }

  .list-task.completed {
    opacity: 0.6;
    text-decoration: line-through;
  }

  .task-date {
    min-width: 100px;
    display: flex;
    align-items: center;
  }

  .date-text {
    font-size: 0.875rem;
    font-weight: 600;
    color: #6b7280;
    text-align: center;
    background: #f3f4f6;
    padding: 0.5rem;
    border-radius: 8px;
  }

  .task-content {
    flex: 1;
  }

  .task-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
  }

  .task-notes {
    font-size: 0.875rem;
    color: #6b7280;
    line-height: 1.4;
  }

  .no-tasks-list {
    text-align: center;
    color: #9ca3af;
    font-style: italic;
    padding: 2rem 0;
    font-size: 1.125rem;
  }

  /* Mobile responsive adjustments for custom views */
  @media (max-width: 768px) {
    .week-grid {
      grid-template-columns: 1fr;
      gap: 0.75rem;
    }

    .week-day-card {
      min-height: auto;
      max-height: 350px;
      padding: 0.75rem;
    }

    .day-tasks {
      max-height: 250px;
    }

    .task-title {
      font-size: 0.875rem;
      -webkit-line-clamp: 2;
    }

    .list-task {
      flex-direction: column;
      gap: 0.75rem;
    }

    .task-date {
      min-width: auto;
    }

    .date-text {
      text-align: left;
      display: inline-block;
      width: auto;
    }
  }
</style>
