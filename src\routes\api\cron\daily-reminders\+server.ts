import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { sendDailyReminders, processReminderNotifications } from '$lib/server/services/emailReminders.js';

export const POST: RequestHandler = async ({ request }) => {
  try {
    // Verify this is a legitimate cron request
    const authHeader = request.headers.get('authorization');
    const cronSecret = process.env.CRON_SECRET || 'dev-secret';
    
    if (authHeader !== `Bearer ${cronSecret}`) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log('Starting daily reminder cron job...');
    
    // Process both daily reminders and specific reminder notifications
    await Promise.all([
      sendDailyReminders(),
      processReminderNotifications()
    ]);

    console.log('Daily reminder cron job completed successfully');
    
    return json({ 
      success: true, 
      message: 'Daily reminders processed successfully',
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Daily reminder cron job failed:', error);
    
    return json({ 
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
};

// Allow GET for testing in development
export const GET: RequestHandler = async ({ url }) => {
  // Only allow in development
  if (process.env.NODE_ENV === 'production') {
    return json({ error: 'Not allowed in production' }, { status: 403 });
  }

  const secret = url.searchParams.get('secret');
  if (secret !== (process.env.CRON_SECRET || 'dev-secret')) {
    return json({ error: 'Invalid secret' }, { status: 401 });
  }

  try {
    console.log('Testing daily reminder system...');
    
    await Promise.all([
      sendDailyReminders(),
      processReminderNotifications()
    ]);

    return json({ 
      success: true, 
      message: 'Test completed successfully',
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Test failed:', error);
    
    return json({ 
      error: 'Test failed',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
};
