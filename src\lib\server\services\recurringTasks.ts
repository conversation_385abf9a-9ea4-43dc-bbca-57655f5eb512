import { db } from '../db/index.js';
import { tasks, taskReminders } from '../db/schema.js';
import type { Task, NewTask, NewTaskReminder } from '../db/schema.js';
import { eq, and, gte, lt, isNull } from 'drizzle-orm';
import { v4 as uuidv4 } from 'uuid';
import { getSchedulerConfig } from '../config/scheduler.js';

// Timezone offset helpers
const TIMEZONE_OFFSETS: Record<string, number> = {
  'Asia/Kuala_Lumpur': 8,
  'Asia/Singapore': 8,
  'Asia/Jakarta': 7,
  'Asia/Bangkok': 7,
  'Asia/Manila': 8,
  'Asia/Hong_Kong': 8,
  'Asia/Shanghai': 8,
  'Asia/Tokyo': 9,
  'UTC': 0,
  'America/New_York': -5,
  'America/Los_Angeles': -8,
  'Europe/London': 0
};

function getTimezoneOffset(tz: string): number {
  return TIMEZONE_OFFSETS[tz] || 8;
}

/**
 * Generate recurring task instances based on recurrence rule
 */
export async function generateRecurringInstances(templateTask: Task, endDate: Date, userTimezone: string = 'Asia/Kuala_Lumpur'): Promise<NewTask[]> {
  if (!templateTask.recurrenceRule) {
    return [];
  }

  // For recurring tasks without dueDate, start from today in user's timezone
  let startDate: Date;
  if (templateTask.dueDate) {
    startDate = new Date(templateTask.dueDate);
  } else {
    // Create today's date in user's timezone
    const now = new Date();
    const offsetHours = getTimezoneOffset(userTimezone);
    startDate = new Date(now.getTime() + (offsetHours * 60 * 60 * 1000));
  }

  const instances: NewTask[] = [];
  const rule = templateTask.recurrenceRule as any;
  const recurringGroupId = templateTask.recurringGroupId || uuidv4();

  // For all recurring tasks, find the first valid occurrence
  let currentDate: Date;
  if (rule.type === 'weekly' && rule.weekdays && rule.weekdays.length > 0) {
    // Find the next occurrence of the specified weekday(s)
    currentDate = getNextOccurrence(new Date(startDate.getTime() - 24 * 60 * 60 * 1000), rule, userTimezone);
  } else if (rule.type === 'monthly' && (rule.monthlyType === 'last_day' || rule.monthlyType === 'last_x_day')) {
    // For last day or last X day patterns, check current month first
    currentDate = getFirstValidOccurrence(startDate, rule, userTimezone);
  } else {
    // For other patterns, find the next occurrence from today
    currentDate = getNextOccurrence(new Date(startDate.getTime() - 24 * 60 * 60 * 1000), rule, userTimezone);
  }

  let instanceCount = 0;
  const maxInstances = 100; // Limit to 100 instances, scheduler will maintain more if needed

  while (instanceCount < maxInstances && currentDate <= endDate) {

    // Always create instances for recurring tasks
    const instance: NewTask = {
      userId: templateTask.userId,
      title: templateTask.title,
      notes: templateTask.notes,
      priority: templateTask.priority,
      categoryId: templateTask.categoryId,
      dueDate: new Date(currentDate),
      completed: false,
      subtasks: templateTask.subtasks,
      recurrenceRule: templateTask.recurrenceRule,
      reminderDays: templateTask.reminderDays,
      recurringGroupId: recurringGroupId,
      isRecurringTemplate: false,
      recurringInstanceDate: new Date(currentDate)
    };
    instances.push(instance);

    // Calculate next occurrence based on rule
    const nextDate = getNextOccurrence(currentDate, rule, userTimezone);

    // Check end conditions before updating currentDate
    if (rule.endType === 'after' && instanceCount >= rule.endAfterOccurrences) {
      break;
    }
    if (rule.endType === 'on' && rule.endOnDate && nextDate > new Date(rule.endOnDate)) {
      break;
    }
    if (nextDate > endDate) {
      break;
    }

    currentDate = nextDate;
    instanceCount++;
  }

  return instances;
}

/**
 * Get the first valid occurrence for a recurring task (current month or next)
 */
function getFirstValidOccurrence(startDate: Date, rule: any, userTimezone: string = 'Asia/Kuala_Lumpur'): Date {
  // First, try to get a date in the current month
  const currentMonthDate = new Date(startDate);

  if (rule.type === 'monthly' && rule.monthlyType === 'last_day') {
    // Last day of current month
    currentMonthDate.setDate(1); // First day of current month
    currentMonthDate.setMonth(currentMonthDate.getMonth() + 1); // Move to next month
    currentMonthDate.setDate(0); // Set to last day of previous month (current month)
  } else if (rule.type === 'monthly' && rule.monthlyType === 'last_x_day') {
    // X days before the end of current month
    const daysBeforeEnd = rule.monthlyLastXDay || 1;
    currentMonthDate.setDate(1); // First day of current month
    currentMonthDate.setMonth(currentMonthDate.getMonth() + 1); // Move to next month
    currentMonthDate.setDate(0); // Set to last day of previous month (current month)
    currentMonthDate.setDate(currentMonthDate.getDate() - (daysBeforeEnd - 1));
  }

  // If the current month's date is still in the future (or today), use it
  if (currentMonthDate >= startDate) {
    return currentMonthDate;
  }

  // Otherwise, get the next occurrence (next month)
  return getNextOccurrence(new Date(startDate.getTime() - 24 * 60 * 60 * 1000), rule, userTimezone);
}

/**
 * Calculate next occurrence date based on recurrence rule
 */
function getNextOccurrence(currentDate: Date, rule: any, userTimezone: string = 'Asia/Kuala_Lumpur'): Date {
  const nextDate = new Date(currentDate);

  switch (rule.type) {
    case 'daily':
      nextDate.setDate(nextDate.getDate() + (rule.interval || 1));
      break;

    case 'weekly':
      if (rule.weekdays && rule.weekdays.length > 0) {
        // Convert UTC date to user timezone for weekday calculation
        const offsetHours = getTimezoneOffset(userTimezone);
        const localDate = new Date(nextDate.getTime() + (offsetHours * 60 * 60 * 1000));
        const currentWeekday = localDate.getDay();
        const sortedWeekdays = [...rule.weekdays].sort((a, b) => a - b);

        let nextWeekday = sortedWeekdays.find(day => day > currentWeekday);
        if (!nextWeekday) {
          // Move to next week and use first weekday
          nextWeekday = sortedWeekdays[0];
          nextDate.setDate(nextDate.getDate() + (7 - currentWeekday + nextWeekday));
        } else {
          nextDate.setDate(nextDate.getDate() + (nextWeekday - currentWeekday));
        }
      } else {
        nextDate.setDate(nextDate.getDate() + (7 * (rule.interval || 1)));
      }
      break;

    case 'monthly':
      if (rule.monthlyType === 'date') {
        nextDate.setMonth(nextDate.getMonth() + (rule.interval || 1));
        nextDate.setDate(rule.monthlyDate || 1);
      } else if (rule.monthlyType === 'weekday') {
        // Every Nth weekday of the month
        nextDate.setMonth(nextDate.getMonth() + (rule.interval || 1));
        const targetWeekday = rule.monthlyWeekday || 1;
        const weekNumber = rule.monthlyWeekNumber || 1;

        // Set to first day of month
        nextDate.setDate(1);

        // Find first occurrence of target weekday
        while (nextDate.getDay() !== targetWeekday) {
          nextDate.setDate(nextDate.getDate() + 1);
        }

        // Add weeks to get to the Nth occurrence
        if (weekNumber === -1) {
          // Last occurrence - find last one in month
          const lastDay = new Date(nextDate.getFullYear(), nextDate.getMonth() + 1, 0);
          while (lastDay.getDay() !== targetWeekday) {
            lastDay.setDate(lastDay.getDate() - 1);
          }
          nextDate.setDate(lastDay.getDate());
        } else {
          nextDate.setDate(nextDate.getDate() + (7 * (weekNumber - 1)));
        }
      } else if (rule.monthlyType === 'last_day') {
        // Last day of the month
        // First set to day 1 to avoid month overflow issues
        nextDate.setDate(1);
        nextDate.setMonth(nextDate.getMonth() + (rule.interval || 1));
        // Set to last day of the month
        nextDate.setMonth(nextDate.getMonth() + 1); // Move to next month
        nextDate.setDate(0); // Set to last day of previous month (which is our target month)
      } else if (rule.monthlyType === 'last_x_day') {
        // X days before the end of the month
        const daysBeforeEnd = rule.monthlyLastXDay || 1;
        // First set to day 1 to avoid month overflow issues
        nextDate.setDate(1);
        nextDate.setMonth(nextDate.getMonth() + (rule.interval || 1));
        // Set to last day of the month
        nextDate.setMonth(nextDate.getMonth() + 1); // Move to next month
        nextDate.setDate(0); // Set to last day of previous month (which is our target month)
        // Subtract the specified number of days (minus 1 because we want X days before, not X+1)
        nextDate.setDate(nextDate.getDate() - (daysBeforeEnd - 1));
      }
      break;

    case 'yearly':
      nextDate.setFullYear(nextDate.getFullYear() + (rule.interval || 1));
      if (rule.yearlyMonth) {
        nextDate.setMonth(rule.yearlyMonth - 1);
      }
      break;

    default:
      nextDate.setDate(nextDate.getDate() + 1);
  }

  return nextDate;
}

/**
 * Create recurring task and generate initial instances
 */
export async function createRecurringTask(taskData: Omit<NewTask, 'id' | 'createdAt' | 'updatedAt'>, userTimezone: string = 'Asia/Kuala_Lumpur'): Promise<Task> {
  const recurringGroupId = uuidv4();

  // Create template task
  const templateData: NewTask = {
    ...taskData,
    recurringGroupId,
    isRecurringTemplate: true
  };

  const [template] = await db.insert(tasks).values(templateData).returning();

  // Generate instances for configured years ahead (scheduler will maintain more if needed)
  const config = getSchedulerConfig();
  const endDate = new Date();
  endDate.setFullYear(endDate.getFullYear() + config.recurringGenerationYears);

  const instances = await generateRecurringInstances(template, endDate, userTimezone);

  console.log(`Generated ${instances.length} recurring instances for template ${template.id}`);

  if (instances.length > 0) {
    await db.insert(tasks).values(instances);
    console.log(`Inserted ${instances.length} recurring task instances`);
  }

  return template;
}

/**
 * Convert a non-recurring task to a recurring task
 */
export async function convertToRecurringTask(
  taskId: string,
  updateData: Partial<Omit<NewTask, 'id' | 'createdAt' | 'updatedAt'>>,
  userTimezone: string = 'Asia/Kuala_Lumpur'
): Promise<Task> {
  // Get the current task
  const [currentTask] = await db.select().from(tasks).where(eq(tasks.id, taskId));

  if (!currentTask) {
    throw new Error('Task not found');
  }

  // Generate a new recurring group ID
  const recurringGroupId = uuidv4();

  // Convert the task to a recurring template
  const templateData = {
    ...updateData,
    isRecurringTemplate: true,
    recurringGroupId,
    updatedAt: new Date()
  };

  const [updatedTemplate] = await db
    .update(tasks)
    .set(templateData)
    .where(eq(tasks.id, taskId))
    .returning();

  // Generate instances for configured years ahead
  const config = getSchedulerConfig();
  const endDate = new Date();
  endDate.setFullYear(endDate.getFullYear() + config.recurringGenerationYears);

  const instances = await generateRecurringInstances(updatedTemplate, endDate, userTimezone);

  console.log(`Generated ${instances.length} recurring instances for converted template ${taskId}`);

  if (instances.length > 0) {
    await db.insert(tasks).values(instances);
    console.log(`Inserted ${instances.length} recurring task instances`);
  }

  return updatedTemplate;
}

/**
 * Update recurring task and regenerate future instances
 */
export async function updateRecurringTask(
  templateId: string,
  updateData: Partial<Omit<NewTask, 'id' | 'createdAt' | 'updatedAt'>>,
  userTimezone: string = 'Asia/Kuala_Lumpur'
): Promise<Task> {
  // Get the current template task
  const [currentTemplate] = await db.select().from(tasks).where(eq(tasks.id, templateId));

  if (!currentTemplate || !currentTemplate.isRecurringTemplate) {
    throw new Error('Template task not found or not a recurring template');
  }

  // Update the template task
  const [updatedTemplate] = await db
    .update(tasks)
    .set({ ...updateData, updatedAt: new Date() })
    .where(eq(tasks.id, templateId))
    .returning();

  // Update all existing uncompleted instances with the new data
  // Only update certain fields that should propagate to instances
  const instanceUpdateData: any = {};
  if (updateData.title !== undefined) instanceUpdateData.title = updateData.title;
  if (updateData.notes !== undefined) instanceUpdateData.notes = updateData.notes;
  if (updateData.priority !== undefined) instanceUpdateData.priority = updateData.priority;
  if (updateData.categoryId !== undefined) instanceUpdateData.categoryId = updateData.categoryId;
  if (updateData.reminderDays !== undefined) instanceUpdateData.reminderDays = updateData.reminderDays;

  if (Object.keys(instanceUpdateData).length > 0) {
    instanceUpdateData.updatedAt = new Date();
    await db.update(tasks)
      .set(instanceUpdateData)
      .where(
        and(
          eq(tasks.recurringGroupId, currentTemplate.recurringGroupId),
          eq(tasks.isRecurringTemplate, false),
          eq(tasks.completed, false)
        )
      );
    console.log(`Updated existing uncompleted instances with new data`);
  }

  // If recurrence rule changed, regenerate all future instances
  if (updateData.recurrenceRule !== undefined) {
    // Delete all future instances (not completed and in the future)
    const now = new Date();
    await db.delete(tasks).where(
      and(
        eq(tasks.recurringGroupId, currentTemplate.recurringGroupId),
        eq(tasks.isRecurringTemplate, false),
        eq(tasks.completed, false),
        gte(tasks.recurringInstanceDate, now)
      )
    );

    // Regenerate instances for configured years ahead
    const config = getSchedulerConfig();
    const endDate = new Date();
    endDate.setFullYear(endDate.getFullYear() + config.recurringGenerationYears);

    const instances = await generateRecurringInstances(updatedTemplate, endDate, userTimezone);

    console.log(`Regenerated ${instances.length} recurring instances for updated template ${templateId}`);

    if (instances.length > 0) {
      await db.insert(tasks).values(instances);
      console.log(`Inserted ${instances.length} new recurring task instances`);
    }
  }

  return updatedTemplate;
}

/**
 * Delete all instances of a recurring task group
 */
export async function deleteRecurringTaskGroup(recurringGroupId: string, userId: string): Promise<void> {
  await db.delete(tasks).where(
    and(
      eq(tasks.recurringGroupId, recurringGroupId),
      eq(tasks.userId, userId)
    )
  );
}

/**
 * Generate reminders for tasks
 */
export async function generateTaskReminders(taskId: string): Promise<void> {
  const [task] = await db.select().from(tasks).where(eq(tasks.id, taskId));

  if (!task || !task.dueDate || task.reminderDays === null) {
    return;
  }

  // Calculate reminder date
  const config = getSchedulerConfig();

  const reminderDate = new Date(task.dueDate);
  reminderDate.setDate(reminderDate.getDate() - (task.reminderDays || config.defaultReminderDays));
  reminderDate.setHours(config.emailReminderHour, config.emailReminderMinute, 0, 0);

  // Only create reminder if it's in the future
  if (reminderDate > new Date()) {
    const reminderData: NewTaskReminder = {
      userId: task.userId,
      taskId: task.id,
      reminderDate,
      reminderType: 'upcoming',
      sent: false
    };

    await db.insert(taskReminders).values(reminderData);
  }
}

/**
 * Get tasks that need reminders today
 */
export async function getTasksNeedingReminders(date: Date = new Date()): Promise<TaskReminder[]> {
  const startOfDay = new Date(date);
  startOfDay.setHours(0, 0, 0, 0);

  const endOfDay = new Date(date);
  endOfDay.setHours(23, 59, 59, 999);

  return await db.select()
    .from(taskReminders)
    .where(
      and(
        gte(taskReminders.reminderDate, startOfDay),
        lt(taskReminders.reminderDate, endOfDay),
        eq(taskReminders.sent, false)
      )
    );
}

/**
 * Mark reminder as sent
 */
export async function markReminderSent(reminderId: string): Promise<void> {
  await db.update(taskReminders)
    .set({ sent: true, sentAt: new Date() })
    .where(eq(taskReminders.id, reminderId));
}
