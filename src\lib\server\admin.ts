import { verifyJWT } from './auth.js';
import { getUserById } from './db/operations.js';
import type { RequestEvent } from '@sveltejs/kit';

export interface AdminUser {
  id: string;
  email: string;
  name: string | null;
  isAdmin: boolean;
}

/**
 * Check if the current user is an admin
 */
export async function isUserAdmin(event: RequestEvent): Promise<AdminUser | null> {
  const token = event.cookies.get('auth-token');
  if (!token) {
    return null;
  }

  const payload = verifyJWT(token);
  if (!payload) {
    return null;
  }

  const user = await getUserById(payload.userId);
  if (!user || !user.isAdmin) {
    return null;
  }

  return {
    id: user.id,
    email: user.email,
    name: user.name,
    isAdmin: user.isAdmin
  };
}

/**
 * Middleware to check admin access
 */
export async function requireAdmin(event: RequestEvent): Promise<AdminUser> {
  const adminUser = await isUserAdmin(event);
  if (!adminUser) {
    throw new Error('Admin access required');
  }
  return adminUser;
}
