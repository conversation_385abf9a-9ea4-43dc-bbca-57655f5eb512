import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { verifyJWT } from '$lib/server/auth.js';
import { createTask, getTasksForTaskList } from '$lib/server/db/operations.js';
import { createRecurringTask, generateTaskReminders } from '$lib/server/services/recurringTasks.js';

export const GET: RequestHandler = async ({ request, cookies }) => {
  try {
    const token = cookies.get('auth-token');
    if (!token) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    const payload = verifyJWT(token);
    if (!payload) {
      return json({ error: 'Invalid token' }, { status: 401 });
    }

    const url = new URL(request.url);
    const completed = url.searchParams.get('completed');
    const completedFilter = completed === 'true' ? true : completed === 'false' ? false : undefined;

    const tasks = await getTasksForTaskList(payload.userId, completedFilter);

    return json({ tasks });
  } catch (error) {
    console.error('Get tasks error:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
};

export const POST: RequestHandler = async ({ request, cookies }) => {
  try {
    const token = cookies.get('auth-token');
    if (!token) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    const payload = verifyJWT(token);
    if (!payload) {
      return json({ error: 'Invalid token' }, { status: 401 });
    }

    const { title, notes, priority, categoryId, dueDate, subtasks, recurrenceRule, reminderDays } = await request.json();

    // Validate required fields
    if (!title || title.trim() === '') {
      return json({ error: 'Title is required' }, { status: 400 });
    }

    // Validate priority
    if (priority !== undefined && (priority < 0 || priority > 2)) {
      return json({ error: 'Priority must be 0 (low), 1 (normal), or 2 (high)' }, { status: 400 });
    }

    // Parse due date if provided
    let parsedDueDate = null;
    if (dueDate) {
      parsedDueDate = new Date(dueDate);
      if (isNaN(parsedDueDate.getTime())) {
        return json({ error: 'Invalid due date format' }, { status: 400 });
      }
    }

    let task;

    console.log('Creating task with recurrenceRule:', recurrenceRule);

    if (recurrenceRule) {
      console.log('Creating recurring task...');
      // Get user timezone for recurring task generation
      const { getUserById } = await import('$lib/server/db/operations.js');
      const user = await getUserById(payload.userId);
      const userTimezone = user?.timezone || 'Asia/Kuala_Lumpur';

      // Create recurring task with instances
      task = await createRecurringTask({
        userId: payload.userId,
        title: title.trim(),
        notes: notes?.trim() || null,
        priority: priority || 1,
        categoryId: categoryId || null,
        dueDate: parsedDueDate,
        subtasks: subtasks || null,
        recurrenceRule: recurrenceRule,
        reminderDays: reminderDays ?? 3
      }, userTimezone);
    } else {
      // Create regular task
      task = await createTask({
        userId: payload.userId,
        title: title.trim(),
        notes: notes?.trim() || null,
        priority: priority || 1,
        categoryId: categoryId || null,
        dueDate: parsedDueDate,
        subtasks: subtasks || null,
        recurrenceRule: null,
        reminderDays: reminderDays ?? 3
      });
    }

    // Generate reminders if task has due date and reminder days is set
    if (task.dueDate && task.reminderDays !== null) {
      if (recurrenceRule) {
        // For recurring tasks, generate reminders for all instances
        const { getTasksByUserId } = await import('$lib/server/db/operations.js');
        const allTasks = await getTasksByUserId(payload.userId);
        const recurringInstances = allTasks.filter(t =>
          t.recurringGroupId === task.recurringGroupId &&
          !t.isRecurringTemplate &&
          t.dueDate
        );

        for (const instance of recurringInstances) {
          await generateTaskReminders(instance.id);
        }
      } else {
        // For regular tasks, generate reminder for this task only
        await generateTaskReminders(task.id);
      }
    }

    return json({ task }, { status: 201 });
  } catch (error) {
    console.error('Create task error:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
};
